{"name": "test-vue2-project", "version": "1.0.0", "description": "Test Vue 2 project for migration", "main": "src/main.js", "dependencies": {"vue": "^3.4.0", "vue-router": "^4.5.0", "vuex": "^4.1.0", "vue-count-to": "^1.0.13", "vuedraggable": "^4.1.0", "vue-splitpane": "^1.0.6", "v-charts": "^1.19.0", "@riophae/vue-treeselect": "^0.4.0", "vue-uuid": "^2.0.2", "vue-clipboard2": "^0.3.3", "vue-lazyload": "^1.3.3", "vue-infinite-scroll": "^2.0.2", "vue-virtual-scroller": "^1.1.2", "vue-awesome-swiper": "^4.1.1", "vue-pdf": "^4.3.0", "vue-qr": "^4.0.9", "vue-cropper": "^0.5.8", "vue-json-editor": "^1.4.3", "vue-codemirror": "^4.0.6", "vue-quill-editor": "^3.0.6", "@tinymce/tinymce-vue": "^3.2.8", "vue-i18n": "^8.27.2", "vue-meta": "^2.4.0", "vue-moment": "^4.1.0", "vue-progressbar": "^0.7.5", "vue-notification": "^1.3.20", "vue-toasted": "^1.1.28", "vue-loading-overlay": "^3.4.2", "vue-skeleton-webpack-plugin": "^1.2.2", "portal-vue": "^2.1.7", "vue-fragment": "^1.6.0", "vue-observe-visibility": "^1.0.0", "vue-resize": "^2.0.0-alpha.1", "vue-clickaway": "^2.2.2", "vue-outside-events": "^1.0.3", "vue-focus": "^2.1.0", "vue-hotkey": "^1.3.1", "vue-shortkey": "^3.1.7", "vue-touch": "^2.0.0-beta.4", "vue-gesture": "^1.0.0", "vue-analytics": "^5.22.1", "vue-gtag": "^1.16.1", "vue-social-sharing": "^3.0.9", "vue-good-table": "^2.21.11", "vue-tables-2": "^2.4.3", "vue-data-tables": "^3.4.5", "vue-grid-layout": "^2.3.12", "vue-masonry-css": "^1.0.3", "vue-waterfall": "^2.0.6", "vue-echarts": "^5.0.0-beta.0", "vue-chartjs": "^3.5.1", "vue-apexcharts": "^1.6.2", "vue-highcharts": "^0.2.0", "vue-d3-network": "^0.1.28", "vue-cal": "^4.8.1", "vue-fullcalendar": "^1.0.9", "vue-datepicker": "^1.3.0", "vue2-datepicker": "^3.10.4", "vue-timepicker": "^0.1.2", "vue-color": "^2.8.1", "vue-slider-component": "^3.2.24", "vue-rate": "^2.2.0", "vue-star-rating": "^1.7.0", "vue-upload-component": "^2.8.22", "vue-image-crop-upload": "^2.5.0", "vue-avatar": "^2.3.3", "vue-gravatar": "^1.4.0", "vue-barcode": "^1.3.0", "vue-qrcode-component": "^2.1.1", "vue-markdown": "^2.2.4", "vue-prism-editor": "^1.3.0", "vue-highlight.js": "^3.1.0", "vue-syntax-highlight": "^1.0.4", "vue-tour": "^1.5.0", "vue-intro": "^1.3.2", "vue-shepherd": "^1.0.2", "vue-cookies": "^1.8.1", "vue-ls": "^3.2.1", "vue-localstorage": "^0.6.2", "vue-sessionstorage": "^1.0.0", "vue-axios": "^3.4.1", "vue-resource": "^1.5.3", "vue-socket.io": "^3.0.10", "vue-native-websocket": "^2.0.15", "vue-sse": "^2.0.7", "vue-worker": "^1.2.1", "vue-web-workers": "^1.0.0", "vue-async-computed": "^3.9.0", "vue-async-data": "^1.0.2", "vue-wait": "^1.4.6", "vue-promised": "^1.0.8", "vue-concurrency": "^2.2.1", "vue-rx": "^6.2.0", "vue-observe": "^1.0.1", "vue-reactive-storage": "^1.0.0", "vue-stash": "^2.0.1-beta", "vue-shared-state": "^1.0.0", "vue-kindergarten": "^0.3.1", "vue-acl": "^4.3.0", "vue-gates": "^2.1.1", "vue-permissions": "^1.0.2", "vue-auth": "^1.4.2", "vue-authenticate": "^1.4.1", "vue-jwt-auth": "^1.3.1", "vue-social-auth": "^1.4.9", "element-plus": "^2.9.0", "@element-plus/icons-vue": "^2.3.1", "@vue/compiler-sfc": "^3.4.0"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "webpack": "^4.46.0", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^4.5.15", "@vue/cli-plugin-pwa": "^4.5.15", "@vue/cli-plugin-unit-jest": "^4.5.15", "@vue/cli-plugin-e2e-cypress": "^4.5.15", "vue-jest": "^3.0.7", "@vue/test-utils": "^2.4.6", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-preset-env": "^1.7.0", "babel-preset-stage-3": "^6.24.1", "css-loader": "^3.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^4.5.2", "mini-css-extract-plugin": "^1.6.2", "node-sass": "^6.0.1", "sass-loader": "^10.2.0", "style-loader": "^2.0.0", "url-loader": "^4.1.1", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "webpack-cli": "^4.9.2", "webpack-dev-server": "^3.11.3", "eslint": "^6.8.0", "eslint-plugin-vue": "^10.2.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "typescript": "^4.5.5", "jest": "^26.6.3", "cypress": "^9.7.0", "cross-env": "^7.0.3", "rimraf": "^3.0.2", "copy-webpack-plugin": "^6.4.1", "terser-webpack-plugin": "^4.2.3", "optimize-css-assets-webpack-plugin": "^6.0.1", "friendly-errors-webpack-plugin": "^1.7.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "vue-svg-loader": "^0.16.0", "svg-sprite-loader": "^6.0.11", "image-webpack-loader": "^8.1.0", "postcss": "^8.4.6", "postcss-loader": "^4.3.0", "autoprefixer": "^10.4.2", "cssnano": "^5.0.17"}, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "build:prod": "cross-env NODE_ENV=production vue-cli-service build", "build:staging": "cross-env NODE_ENV=staging vue-cli-service build", "analyze": "vue-cli-service build --analyze", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run serve"}}