<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><el-icon-s-data /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalUsers }}</h3>
              <p>Total Users</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><el-icon-s-order /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalOrders }}</h3>
              <p>Total Orders</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><el-icon-s-marketing /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalRevenue }}</h3>
              <p>Total Revenue</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><el-icon-s-goods /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalProducts }}</h3>
              <p>Total Products</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="16">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Sales Overview</span>
            </div>
          </template>
          <ve-line :data="salesData" :settings="salesSettings" />
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Recent Activities</span>
            </div>
          </template>
          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <i :class="activity.icon"></i>
              </div>
              <div class="activity-content">
                <p>{{ activity.text }}</p>
                <small>{{ activity.time }}</small>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  SData as ElIconSData,
  SOrder as ElIconSOrder,
  SMarketing as ElIconSMarketing,
  SGoods as ElIconSGoods,
} from '@element-plus/icons'
import VeLine from 'v-charts/lib/line.common'

export default {
  components: {
    VeLine,
    ElIconSData,
    ElIconSOrder,
    ElIconSMarketing,
    ElIconSGoods,
  },
  name: 'Dashboard',
  data() {
    return {
      totalUsers: 1234,
      totalOrders: 567,
      totalRevenue: '$12,345',
      totalProducts: 89,
      salesData: {
        columns: ['date', 'sales', 'orders'],
        rows: [
          { date: '2024-01-01', sales: 1000, orders: 50 },
          { date: '2024-01-02', sales: 2000, orders: 100 },
          { date: '2024-01-03', sales: 1500, orders: 75 },
          { date: '2024-01-04', sales: 3000, orders: 150 },
          { date: '2024-01-05', sales: 2500, orders: 125 },
        ],
      },
      salesSettings: {
        yAxisName: ['Sales ($)', 'Orders'],
      },
      recentActivities: [
        {
          id: 1,
          text: 'New user registered',
          icon: 'el-icon-user',
          time: '2 min ago',
        },
        {
          id: 2,
          text: 'Order #1234 completed',
          icon: 'el-icon-s-order',
          time: '5 min ago',
        },
        {
          id: 3,
          text: 'Product inventory updated',
          icon: 'el-icon-s-goods',
          time: '10 min ago',
        },
        {
          id: 4,
          text: 'Payment received',
          icon: 'el-icon-money',
          time: '15 min ago',
        },
      ],
    }
  },
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
.stat-card {
  text-align: center;
}
.stat-content {
  display: flex;
  align-items: center;
  justify-content: center;
}
.stat-icon {
  font-size: 48px;
  color: #409eff;
  margin-right: 20px;
}
.stat-info h3 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}
.stat-info p {
  margin: 5px 0 0 0;
  color: #909399;
}
.activity-list {
  max-height: 300px;
  overflow-y: auto;
}
.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}
.activity-item:last-child {
  border-bottom: none;
}
.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.activity-icon i {
  color: #409eff;
  font-size: 18px;
}
.activity-content p {
  margin: 0;
  color: #303133;
}
.activity-content small {
  color: #909399;
}
</style>
