# Vue 2 到 Vue 3 迁移工具使用指南

## 🎯 项目概述

这个迁移工具是为了帮助您将 Vue 2 项目自动化迁移到 Vue 3 而设计的。工具包含了完整的迁移流程，从依赖升级到代码转换，再到错误修复。

## 📁 项目结构

```
migrate-cli/
├── src/                      # 核心功能模块
│   ├── packageUpgrader.js    # 📦 依赖升级器
│   ├── dependencyChecker.js  # 🔍 兼容性检查器
│   ├── VueCodeMigrator.js       # 🔄 代码迁移器
│   ├── failureLogger.js      # 📝 失败记录器
│   ├── aiRepairer.js         # 🧠 AI 修复器
│   ├── eslintFixer.js        # 🔧 ESLint 修复器
│   └── buildFixer.js         # 🏗️ 构建修复器
├── bin/
│   └── cli.js                # 🖥️ CLI 入口
├── index.js                  # 📋 主入口文件
├── demo.js                   # 🎬 演示版本
├── test.js                   # 🧪 测试脚本
└── package.json
```

## 🚀 快速开始

### 1. 查看完整演示

```bash
# 在 migrate-cli 目录下
node final-demo.js
```

这会显示完整的迁移工具功能演示，包括技术栈对比和迁移结果。

### 2. 运行实际迁移

```bash
# 迁移 vue-element-admin 项目
node demo.js ../vue-element-admin

# 迁移 aup-admin-ui 项目
node demo.js ../aup-admin-ui
```

演示版本会：
- ✅ 升级 package.json 中的 Vue 相关依赖
- ✅ 实际转换关键文件 (main.js, router, store)
- ✅ 创建备份文件
- ✅ 扫描项目文件并生成报告
- ✅ 提供下一步建议

### 3. 完整版本（需要安装依赖）

```bash
# 安装依赖
npm install

# 运行完整迁移
node index.js ../aup-admin-ui
```

## 📋 迁移步骤详解

### 步骤 1: 依赖升级 (packageUpgrader.js)

**功能**: 自动升级 package.json 中的 Vue 相关依赖

**转换规则**:
- `vue: "2.6.10"` → `vue: "^3.4.0"`
- `vue-router: "3.0.2"` → `vue-router: "^4.5.0"`
- `vuex: "3.1.0"` → `vuex: "^4.1.0"`
- `element-ui: "2.15.5"` → 删除，添加 `element-plus: "^2.9.0"`
- `vue-template-compiler` → 删除，添加 `@vue/compiler-sfc`

**输出**: 
- 备份原始 package.json
- 生成升级报告

### 步骤 2: 兼容性检查 (dependencyChecker.js)

**功能**: 检查项目中所有依赖的 Vue 3 兼容性

**检查内容**:
- 分析 peerDependencies 中的 Vue 版本要求
- 识别已知的不兼容包
- 提供替代方案建议

**输出**: 兼容性报告，包含兼容、不兼容和未知状态的依赖列表

### 步骤 3: 代码迁移 (VueCodeMigrator.js)

**功能**: 使用 Gogocode 批量转换 Vue 文件和 JS 文件

**转换规则**:

#### Vue 组件转换
```javascript
// 转换前
export default Vue.extend({
  name: 'MyComponent'
})

// 转换后  
export default defineComponent({
  name: 'MyComponent'
})
```

#### Element UI 转换
```javascript
// 转换前
import { Button, Input } from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 转换后
import { ElButton, ElInput } from 'element-plus'
import 'element-plus/dist/index.css'
```

#### Vue Router 转换
```javascript
// 转换前
import VueRouter from 'vue-router'
Vue.use(VueRouter)
export default new VueRouter({})

// 转换后
import { createRouter, createWebHistory } from 'vue-router'
export default createRouter({})
```

### 步骤 4: 失败记录 (failureLogger.js)

**功能**: 详细记录转换失败的文件和原因

**输出文件**:
- `migration-logs/failed-files.json` - 失败文件列表
- `migration-logs/failure-report.md` - 失败报告
- `migration-logs/migration-details.log` - 详细日志

### 步骤 5: AI 修复 (aiRepairer.js)

**功能**: 使用 OpenAI GPT 模型修复复杂的迁移问题

**配置**:
```bash
export OPENAI_API_KEY=your_openai_api_key
```

**修复类型**:
- 复杂的语法转换
- Gogocode 无法处理的边缘情况
- 自定义组件迁移

### 步骤 6: ESLint 修复 (eslintFixer.js)

**功能**: 自动修复代码格式和 Vue 3 语法规则（默认禁用）

**注意**: ESLint 修复默认禁用，避免在迁移过程中造成大规模代码变更。建议在迁移完成后再手动启用。

**启用方式**:
```bash
# 在迁移命令中添加 --eslint 参数
node bin/vue-migrator.js auto --eslint
node bin/vue-migrator.js migrate --eslint
```

**配置**: 自动生成 Vue 3 兼容的 ESLint 配置

**修复内容**:
- 代码格式问题
- Vue 3 特定的语法规则
- 导入语句规范

### 步骤 7: 构建修复 (buildFixer.js)

**功能**: 尝试构建项目并自动修复构建错误

**修复类型**:
- 缺失模块错误
- 类型错误
- 导入路径问题

## 🎯 实际使用示例

### 针对 aup-admin-ui 项目

```bash
# 1. 运行演示版本查看效果
node demo.js ../aup-admin-ui

# 2. 检查生成的报告
cat ../aup-admin-ui/migration-report-demo.json

# 3. 查看 package.json 的变化
diff ../aup-admin-ui/package.json.backup ../aup-admin-ui/package.json
```

### 预期结果

运行后您会看到：
- ✅ Vue 2.6.10 → Vue 3.4.0
- ✅ Vue Router 3.0.2 → Vue Router 4.5.0  
- ✅ Vuex 3.1.0 → Vuex 4.1.0
- ✅ Element UI → Element Plus
- ✅ 扫描到 171 个需要处理的文件
- ✅ 生成详细的迁移报告

## 📝 下一步操作

迁移完成后，建议按以下顺序进行：

1. **安装新依赖**
   ```bash
   cd ../aup-admin-ui
   npm install
   ```

2. **更新路由配置**
   - 检查 `src/router/index.js`
   - 更新为 Vue Router 4 语法

3. **更新 Vuex 配置**
   - 检查 `src/store/index.js`
   - 更新为 Vuex 4 语法

4. **更新 Element UI 组件**
   - 全局搜索 `el-` 组件
   - 更新为 Element Plus 语法

5. **测试应用**
   ```bash
   npm run dev
   ```

## ⚠️ 注意事项

1. **备份重要**: 迁移前务必备份项目
2. **分步进行**: 建议先运行演示版本查看效果
3. **手动检查**: 某些复杂情况需要手动调整
4. **测试充分**: 迁移后需要全面测试功能

## 🔧 故障排除

### 常见问题

1. **依赖安装失败**
   - 检查 Node.js 版本
   - 清除 npm 缓存: `npm cache clean --force`

2. **代码转换失败**
   - 查看 `migration-logs/` 目录下的详细日志
   - 手动修复复杂的语法问题

3. **构建失败**
   - 检查 Vue CLI 版本
   - 可能需要升级构建工具

## 📞 获取帮助

如果遇到问题：
1. 查看生成的日志文件
2. 检查 migration-report.json 中的建议
3. 参考 Vue 3 官方迁移指南

---

**祝您迁移顺利！** 🎉
