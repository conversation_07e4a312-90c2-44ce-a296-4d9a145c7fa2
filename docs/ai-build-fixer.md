# AI 构建错误修复功能

本文档介绍 Vue 2 到 Vue 3 迁移工具中的 AI 构建错误修复功能。

## 概述

AI 构建错误修复功能是迁移工具的第 7 步，专门用于修复 Vue 2 到 Vue 3 迁移过程中出现的构建错误。该功能结合了传统的错误模式匹配和 AI 智能修复，能够自动解决大部分常见的构建问题。

## 功能特性

### 🔍 智能错误检测
- **多种错误类型支持**：TypeScript、Vue 编译、Webpack/Vite、ESLint 错误
- **错误分类**：自动将错误分类为缺失模块、属性不存在、Vue 版本兼容性、UI 库兼容性等
- **上下文感知**：根据文件类型和错误位置提供精确的修复建议

### 🤖 AI 智能修复
- **专用提示词**：为构建错误设计的专门 AI 提示词模板
- **多 LLM 支持**：支持 DeepSeek、GLM、OpenAI 等多个 AI 提供商
- **渐进式修复**：先尝试简单的模式匹配修复，再使用 AI 修复复杂问题

### 🛡️ 安全保障
- **自动备份**：修复前自动备份原文件
- **内容验证**：修复后验证代码的完整性和正确性
- **回滚机制**：修复失败时可以回滚到原始状态

## 使用方法

### 基本用法

```bash
# 完整迁移（包含构建错误修复）
vue-migrator migrate /path/to/project

# 仅运行构建错误修复
vue-migrator build-fix /path/to/project
```

### 配置选项

```bash
# 自定义构建命令
vue-migrator migrate /path/to/project --build-command "npm run build:prod"

# 设置最大重试次数
vue-migrator migrate /path/to/project --max-retries 5

# 跳过构建错误修复
vue-migrator migrate /path/to/project --skip-build
```

## 环境配置

### AI 服务配置

创建 `.env` 文件并配置 AI 服务提供商：

```bash
# DeepSeek (推荐，优先级最高)
DEEPSEEK_TOKEN=your_deepseek_token
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# GLM (智谱AI，备选)
GLM_TOKEN=your_glm_token
LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
LLM_MODEL=glm-4-air

# OpenAI (备选)
OPENAI_API_KEY=your_openai_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
```

### 优先级顺序

AI 服务提供商的优先级顺序为：
1. **DeepSeek** - 推荐使用，专门针对代码生成优化
2. **GLM** - 国内可用的替代方案
3. **OpenAI** - 国际通用方案

## 支持的错误类型

### 1. 缺失模块错误 (missing-module)

**错误示例**：
```
Cannot find module 'element-ui'
```

**修复策略**：
- 自动将 `element-ui` 替换为 `element-plus`
- 将 `vue-template-compiler` 替换为 `@vue/compiler-sfc`
- 移除 Vue 3 内置的模块（如 `@vue/composition-api`）

### 2. 属性不存在错误 (property-not-exist)

**错误示例**：
```
Property 'Vue' does not exist on type
```

**修复策略**：
- 使用 AI 分析上下文并提供正确的 Vue 3 API 替换
- 更新组件实例的属性访问方式
- 修复 `this.$refs` 的访问方式

### 3. Vue 版本兼容性错误 (vue-version)

**错误示例**：
```
Vue.extend is not a function
```

**修复策略**：
- 将 `Vue.extend` 替换为 `defineComponent`
- 将 `new Vue()` 替换为 `createApp()`
- 更新生命周期钩子名称

### 4. UI 库兼容性错误 (ui-library)

**错误示例**：
```
el-button component not found
```

**修复策略**：
- 更新 Element UI 组件名称为 Element Plus
- 修复组件属性和事件名称
- 更新图标引用方式

## API 参考

### BuildFixer 类

```javascript
const BuildFixer = require('./src/buildFixer');

const buildFixer = new BuildFixer(projectPath, {
  buildCommand: 'npm run build',  // 构建命令
  maxRetries: 3,                  // 最大重试次数
  maxTokens: 4000,               // AI 最大 token 数
  temperature: 0.1               // AI 温度参数
});

// 执行构建和修复
const result = await buildFixer.buildAndFix();
```

### 返回结果

```javascript
{
  success: true,           // 是否成功
  attempts: 2,            // 构建尝试次数
  errorsFixed: 5,         // 修复的错误数量
  remainingErrors: 0      // 剩余错误数量
}
```

## 最佳实践

### 1. 渐进式修复
建议按以下顺序进行迁移：
1. 使用 Gogocode 进行批量转换
2. 运行 ESLint 自动修复
3. 使用 AI 修复剩余问题
4. 运行构建错误修复

### 2. 错误处理
- 始终检查修复结果的 `success` 字段
- 保留备份文件以便回滚
- 对于复杂错误，可能需要手动干预

### 3. 性能优化
- 设置合理的 `maxRetries` 值（建议 3-5 次）
- 使用较低的 `temperature` 值（0.1-0.3）以获得更稳定的结果
- 优先使用 DeepSeek 以获得更好的代码生成质量

## 故障排除

### 常见问题

**Q: AI 修复功能不可用**
A: 检查是否正确配置了环境变量，确保至少设置了一个 AI 提供商的 token。

**Q: 修复后代码仍有错误**
A: 可能是复杂的业务逻辑问题，需要手动检查和修复。查看备份文件对比修改内容。

**Q: 构建命令执行失败**
A: 检查 `buildCommand` 配置是否正确，确保在项目目录下可以正常执行。

### 调试模式

```bash
# 启用详细输出
vue-migrator migrate /path/to/project --verbose

# 查看 AI 提供商状态
node -e "console.log(require('./src/aiService').getLLMProviderStatus())"
```

## 测试

运行测试套件验证功能：

```bash
# 运行所有测试
npm test

# 运行构建修复相关测试
npm run test:build

# 运行集成测试
npm run test:integration
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进 AI 构建错误修复功能。请确保：

1. 添加相应的测试用例
2. 更新文档
3. 遵循现有的代码风格

## 许可证

本项目采用 MIT 许可证。
