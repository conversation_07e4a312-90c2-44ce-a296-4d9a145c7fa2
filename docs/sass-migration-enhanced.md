# 增强版 Sass 迁移工具使用指南

## 概述

增强版 Sass 迁移工具是一个全面的自动化解决方案，专门用于将传统的 `@import` 语法迁移到现代的 `@use` 和 `@forward` 模块系统。它不仅处理基础的语法转换，还提供了架构重构、错误诊断、配置优化等高级功能。

## 主要特性

### 🎯 核心功能

1. **智能路径解析**
   - 自动处理 `~` 别名（如 `~element-ui/...`）
   - 解析相对路径和绝对路径
   - 集成 Vite 配置的路径别名

2. **架构重构引擎**
   - 自动生成桶文件（Barrel Files）
   - 检测和修复循环依赖
   - 提取共享变量、混入和函数

3. **Element Plus 专项迁移**
   - 自动转换 Element UI 主题配置
   - 生成现代化的 `@forward` 配置
   - 处理变量名映射

4. **智能错误诊断**
   - 识别常见迁移错误
   - 提供具体的修复建议
   - 支持自动修复

5. **Vite 配置优化**
   - 自动检测和优化 Vite 配置
   - 确保 Sass 模块系统正确集成
   - 添加必要的 `loadPaths` 配置

6. **迁移验证**
   - 自动验证迁移结果
   - 尝试构建项目检测问题
   - 生成详细的迁移报告

## 安装和使用

### 基础使用

```bash
# 使用增强版迁移器（推荐）
sass-migrator-vue enhanced /path/to/your/project

# 预览模式，不实际修改文件
sass-migrator-vue enhanced /path/to/your/project --dry-run

# 显示详细输出
sass-migrator-vue enhanced /path/to/your/project --verbose
```

### 高级选项

```bash
# 禁用特定功能
sass-migrator-vue enhanced /path/to/your/project \
  --no-architecture-refactor \
  --no-element-plus \
  --no-vite-optimization

# 自定义构建命令
sass-migrator-vue enhanced /path/to/your/project \
  --build-command "npm run build:prod"

# 自定义文件匹配模式
sass-migrator-vue enhanced /path/to/your/project \
  --include "src/**/*.scss" "styles/**/*.sass" \
  --exclude "node_modules/**" "dist/**"
```

## 迁移流程

### 阶段 1: 预检查和初始化
- 检查项目结构和依赖
- 初始化路径解析器
- 扫描 Sass 文件

### 阶段 2: Vite 配置优化
- 检测现有 Vite 配置
- 添加必要的 `loadPaths` 配置
- 优化路径别名设置

### 阶段 3: Element Plus 主题迁移
- 检测 Element UI 使用情况
- 转换主题配置为 Element Plus 格式
- 生成现代化的 `@forward` 配置

### 阶段 4: 架构重构
- 分析现有文件结构
- 检测循环依赖
- 生成桶文件和重构架构

### 阶段 5: 基础 Sass 语法迁移
- 转换 `@import` 为 `@use`
- 智能路径解析和修复
- 处理命名空间

### 阶段 6: 错误诊断和修复
- 尝试构建项目获取错误信息
- 智能分析错误类型
- 提供修复建议和自动修复

### 阶段 7: 验证和测试
- 检查剩余的 `@import` 语句
- 验证项目构建状态
- 生成迁移报告

## 配置选项

### 基础配置

```javascript
{
  backup: true,              // 是否创建备份文件
  verbose: false,            // 是否显示详细输出
  dryRun: false,            // 预览模式
  autoFix: true,            // 是否自动修复错误
  include: ['**/*.scss', '**/*.sass'],  // 包含的文件模式
  exclude: ['node_modules/**', 'dist/**', 'build/**']  // 排除的文件模式
}
```

### 功能开关

```javascript
{
  enableArchitectureRefactor: true,    // 启用架构重构
  enableElementPlusMigration: true,    // 启用 Element Plus 迁移
  enableViteOptimization: true,        // 启用 Vite 配置优化
  enableErrorDiagnostic: true,         // 启用错误诊断
  buildCommand: 'npm run build'        // 构建命令
}
```

## 迁移前后对比

### 迁移前（旧的 @import 方式）

```scss
// src/styles/variables.scss
$primary-color: #409eff;
$font-size-base: 14px;

// src/styles/main.scss
@import "~element-ui/packages/theme-chalk/src/index";
@import "./variables";
@import "./mixins";

.my-component {
  color: $primary-color;
  font-size: $font-size-base;
}
```

### 迁移后（新的 @use 方式）

```scss
// src/styles/utils/_variables.scss
$primary-color: #409eff !default;
$font-size-base: 14px !default;

// src/styles/element/index.scss
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #409eff,
    ),
  ),
  $font-path: 'element-plus/dist/fonts'
);

// src/styles/index.scss (桶文件)
@forward 'utils/variables';
@forward 'utils/mixins';

// src/styles/main.scss
@use 'src/styles' as *;
@use 'src/styles/element' as *;

.my-component {
  color: $primary-color;
  font-size: $font-size-base;
}
```

## 常见问题和解决方案

### Q: 迁移后出现 "Undefined variable" 错误

**A:** 这通常是因为变量没有正确的命名空间。解决方案：

1. 确保使用了桶文件：`@use 'src/styles' as *;`
2. 检查变量是否在正确的文件中定义
3. 使用错误诊断功能：`--verbose` 选项

### Q: 出现 "Module loop" 错误

**A:** 这是循环依赖导致的。解决方案：

1. 工具会自动检测并提供修复建议
2. 使用架构重构功能重新组织文件结构
3. 避免文件自引用

### Q: Element UI 样式没有正确迁移

**A:** 确保：

1. 启用了 Element Plus 迁移功能
2. 正确安装了 Element Plus
3. 在主入口文件中导入了新的主题配置

### Q: Vite 构建失败

**A:** 检查：

1. Vite 配置是否包含正确的 `loadPaths`
2. 路径别名是否正确配置
3. 使用 Vite 配置优化功能

## 最佳实践

### 1. 迁移前准备

- 确保项目在迁移前能正常构建
- 提交所有未保存的更改
- 备份重要文件

### 2. 迁移过程

- 首次运行使用 `--dry-run` 预览
- 启用 `--verbose` 查看详细信息
- 分阶段进行，先处理简单文件

### 3. 迁移后验证

- 运行完整的项目构建
- 检查所有页面的样式显示
- 运行测试套件确保功能正常

### 4. 团队协作

- 更新项目文档说明新的导入方式
- 配置 ESLint/Stylelint 规则强制使用新语法
- 培训团队成员了解新的模块系统

## 故障排除

### 启用详细日志

```bash
sass-migrator-vue enhanced /path/to/your/project --verbose
```

### 检查特定错误

```bash
# 仅运行错误诊断
sass-migrator-vue enhanced /path/to/your/project \
  --no-architecture-refactor \
  --no-element-plus \
  --no-vite-optimization
```

### 恢复备份

```bash
sass-migrator-vue restore-backups /path/to/your/project
```

### 清理备份文件

```bash
sass-migrator-vue clean-backups /path/to/your/project
```

## 技术支持

如果遇到问题，请：

1. 查看生成的迁移报告
2. 检查错误诊断结果
3. 参考本文档的常见问题部分
4. 提交 Issue 并附上详细的错误信息

## 更新日志

### v2.0.0 (增强版)
- 新增智能路径解析器
- 新增架构重构引擎
- 新增 Element Plus 专项迁移
- 新增错误诊断系统
- 新增 Vite 配置优化
- 新增迁移验证功能

### v1.0.0 (基础版)
- 基础的 @import 到 @use 转换
- 文件备份和恢复
- 简单的错误处理
