# Vue Element Admin 自动迁移指南

## 🚀 快速开始

### 一键自动迁移（推荐）

```bash
# 进入 migrate-cli 目录
cd migrate-cli

# 自动迁移 vue-element-admin 项目
node bin/vue-migrator.js auto /path/to/vue-element-admin

# 或使用快速脚本
./scripts/migrate-vue-element-admin.sh /path/to/vue-element-admin
```

### Windows 用户

```cmd
REM 进入 migrate-cli 目录
cd migrate-cli

REM 自动迁移 vue-element-admin 项目
node bin\vue-migrator.js auto C:\path\to\vue-element-admin

REM 或使用快速脚本
scripts\migrate-vue-element-admin.bat C:\path\to\vue-element-admin
```

## 🎯 自动化特性

### 智能项目检测
- ✅ 自动识别 Vue Element Admin 项目
- ✅ 检测项目结构和依赖
- ✅ 应用最佳迁移配置

### 无感配置
- ✅ 自动选择构建命令 (`npm run build:prod`)
- ✅ 智能选择 AI 提供商 (DeepSeek > GLM > OpenAI)
- ✅ 自动备份项目文件
- ✅ 预设文件过滤规则

### AI 智能修复
- ✅ 支持 DeepSeek API（推荐，性价比高）
- ✅ 支持 GLM API（国产，速度快）
- ✅ 支持 OpenAI API（兼容性好）

## 🔧 环境配置

### 设置 AI API Key（可选但推荐）

```bash
# DeepSeek（推荐）
export DEEPSEEK_API_KEY="your_deepseek_api_key"

# 或者 GLM
export GLM_API_KEY="your_glm_api_key"

# 或者 OpenAI
export OPENAI_API_KEY="your_openai_api_key"
```

### Windows 环境变量

```cmd
set DEEPSEEK_API_KEY=your_deepseek_api_key
```

## 📋 迁移流程

### 自动执行的步骤

1. **项目检测** - 识别 Vue Element Admin 项目
2. **配置加载** - 应用专用预设配置
3. **项目备份** - 自动备份到 `migration-backup` 目录
4. **依赖升级** - 升级 Vue 3 相关依赖
5. **代码迁移** - 使用 Gogocode 批量转换
6. **AI 修复** - 智能修复转换失败的文件
7. **ESLint 修复** - 自动修复代码格式
8. **构建测试** - 尝试构建并修复错误

### 预设配置内容

```json
{
  "buildCommand": "npm run build:prod",
  "skipDependencyCheck": false,
  "skipAIRepair": false,
  "skipESLint": false,
  "skipBuild": false,
  "aiProvider": "deepseek",
  "verbose": true
}
```

## 🎛️ 命令选项

### 基本用法

```bash
# 自动迁移（推荐）
vue-migrator auto [project-path]

# 预览模式（不实际修改文件）
vue-migrator auto --dry-run

# 指定 AI API Key
vue-migrator auto --ai-key your_api_key

# 强制迁移（即使检测到 Vue 3）
vue-migrator auto --force

# 显示详细信息
vue-migrator auto --verbose
```

### 其他命令

```bash
# 查看所有可用命令
vue-migrator --help

# 查看迁移步骤说明
vue-migrator steps

# 分析项目并生成迁移策略
vue-migrator analyze

# 传统迁移模式
vue-migrator migrate
```

## 📁 项目结构要求

### Vue Element Admin 项目特征

- ✅ `package.json` 中包含 `"name": "vue-element-admin"`
- ✅ 依赖包含 `element-ui`、`vue-router`、`vuex`
- ✅ 存在 `src/layout`、`src/views`、`src/router` 目录
- ✅ Vue 版本为 2.x

### 自动检测指标

| 指标 | 权重 | 说明 |
|------|------|------|
| 包名匹配 | 30% | package.json 中的 name 字段 |
| 依赖检查 | 25% | 关键依赖包的存在 |
| 目录结构 | 25% | 关键目录的存在 |
| Vue 版本 | 15% | Vue 2.x 版本检查 |
| 脚本命令 | 5% | 特定构建脚本的存在 |

## 🔍 故障排除

### 常见问题

1. **项目检测失败**
   ```bash
   # 手动指定项目类型
   vue-migrator migrate --verbose
   ```

2. **AI 修复失败**
   ```bash
   # 跳过 AI 修复
   vue-migrator auto --skip-ai
   ```

3. **构建失败**
   ```bash
   # 跳过构建步骤
   vue-migrator auto --skip-build
   ```

4. **依赖冲突**
   ```bash
   # 跳过依赖检查
   vue-migrator auto --skip-dependency-check
   ```

### 日志文件

- 迁移日志：`vue-element-admin-migration.log`
- 失败文件记录：`migration-failures.json`
- 迁移报告：`migration-report.json`

## 📝 迁移后检查清单

### 必须检查的项目

- [ ] 运行 `npm install` 安装新依赖
- [ ] 运行 `npm run build:prod` 检查构建
- [ ] 检查路由配置是否正常
- [ ] 检查 Vuex 状态管理是否正常
- [ ] 检查 Element Plus 组件是否正常显示
- [ ] 测试主要功能页面

### 可能需要手动调整的内容

- 🔧 路由守卫配置
- 🔧 Vuex 模块配置
- 🔧 Element Plus 主题配置
- 🔧 自定义组件的 API 调整
- 🔧 第三方插件兼容性

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看详细日志：`--verbose` 选项
2. 使用预览模式：`--dry-run` 选项
3. 查看迁移报告：`migration-report.json`
4. 检查失败文件：`migration-failures.json`

## 🎉 成功案例

使用自动迁移工具成功迁移的 Vue Element Admin 项目特点：

- ✅ 标准的 Vue Element Admin 项目结构
- ✅ 使用官方推荐的依赖版本
- ✅ 没有过度自定义的构建配置
- ✅ 遵循 Vue 2 最佳实践

迁移成功率：**85%+** （基于标准 Vue Element Admin 项目）
