const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Element Plus 主题迁移器
 * 专门处理从 Element UI 到 Element Plus 的主题迁移
 */
class ElementPlusMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      elementDir: 'src/styles/element',
      verbose: false,
      ...options
    };
    
    // Element UI 到 Element Plus 的映射
    this.pathMappings = {
      // 主题文件映射
      '~element-ui/packages/theme-chalk/src/index': 'element-plus/theme-chalk/src/index.scss',
      '~element-ui/lib/theme-chalk/index.css': 'element-plus/dist/index.css',
      
      // 变量文件映射
      '~element-ui/packages/theme-chalk/src/common/var': 'element-plus/theme-chalk/src/common/var.scss',
      
      // 组件样式映射
      '~element-ui/packages/theme-chalk/src/button': 'element-plus/theme-chalk/src/button.scss',
      '~element-ui/packages/theme-chalk/src/input': 'element-plus/theme-chalk/src/input.scss',
      '~element-ui/packages/theme-chalk/src/table': 'element-plus/theme-chalk/src/table.scss',
      '~element-ui/packages/theme-chalk/src/form': 'element-plus/theme-chalk/src/form.scss'
    };
    
    // 变量名映射（Element UI -> Element Plus）
    this.variableMappings = {
      // 颜色变量
      '$--color-primary': '$el-color-primary',
      '$--color-success': '$el-color-success',
      '$--color-warning': '$el-color-warning',
      '$--color-danger': '$el-color-danger',
      '$--color-info': '$el-color-info',
      
      // 字体变量
      '$--font-size-base': '$el-font-size-base',
      '$--font-size-small': '$el-font-size-small',
      '$--font-size-large': '$el-font-size-large',
      
      // 边框变量
      '$--border-radius-base': '$el-border-radius-base',
      '$--border-color-base': '$el-border-color-base',
      
      // 间距变量
      '$--button-padding-vertical': '$el-button-padding-vertical',
      '$--button-padding-horizontal': '$el-button-padding-horizontal'
    };
    
    // 默认主题配置
    this.defaultTheme = {
      colors: {
        primary: {
          base: '#409eff'
        },
        success: {
          base: '#67c23a'
        },
        warning: {
          base: '#e6a23c'
        },
        danger: {
          base: '#f56c6c'
        },
        info: {
          base: '#909399'
        }
      },
      fontSize: {
        base: '14px',
        small: '12px',
        large: '16px'
      },
      borderRadius: {
        base: '4px'
      }
    };
  }

  /**
   * 执行 Element Plus 迁移
   */
  async migrate() {
    console.log(chalk.blue('🎨 开始 Element Plus 主题迁移...'));
    
    try {
      // 1. 检测 Element UI 使用情况
      const elementUsage = await this.detectElementUIUsage();
      
      if (!elementUsage.hasElementUI && elementUsage.importStatements.length === 0) {
        console.log(chalk.gray('未检测到 Element UI 使用，跳过迁移'));
        return { skipped: true };
      }
      
      // 2. 分析现有主题配置
      const currentTheme = await this.analyzeCurrentTheme();
      
      // 3. 生成 Element Plus 配置
      await this.generateElementPlusConfig(currentTheme);
      
      // 4. 更新导入语句
      await this.updateImportStatements();
      
      // 5. 生成迁移报告
      const report = await this.generateMigrationReport(elementUsage, currentTheme);
      
      console.log(chalk.green('✅ Element Plus 主题迁移完成'));
      
      return report;
      
    } catch (error) {
      console.error(chalk.red(`Element Plus 迁移失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 检测 Element UI 使用情况
   */
  async detectElementUIUsage() {
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    const usage = {
      hasElementUI: false,
      hasElementPlus: false,
      elementUIVersion: null,
      elementPlusVersion: null,
      importStatements: []
    };
    
    // 检查 package.json
    if (await fs.pathExists(packageJsonPath)) {
      const packageJson = await fs.readJson(packageJsonPath);
      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      if (allDeps['element-ui']) {
        usage.hasElementUI = true;
        usage.elementUIVersion = allDeps['element-ui'];
      }
      
      if (allDeps['element-plus']) {
        usage.hasElementPlus = true;
        usage.elementPlusVersion = allDeps['element-plus'];
      }
    }
    
    // 扫描文件中的导入语句
    const sassFiles = await this.findSassFiles();
    
    for (const filePath of sassFiles) {
      const content = await fs.readFile(filePath, 'utf8');
      const imports = this.extractElementImports(content);
      
      if (imports.length > 0) {
        usage.importStatements.push({
          file: path.relative(this.projectPath, filePath),
          imports
        });
      }
    }
    
    return usage;
  }

  /**
   * 查找 Sass 文件
   */
  async findSassFiles() {
    const { glob } = require('glob');
    
    return await glob('**/*.{scss,sass}', {
      cwd: this.projectPath,
      absolute: true,
      ignore: ['node_modules/**', 'dist/**', 'build/**']
    });
  }

  /**
   * 提取 Element 相关导入
   */
  extractElementImports(content) {
    const imports = [];
    const importRegex = /@(?:import|use)\s+['"]([^'"]*element-ui[^'"]*)['"](?:[^;]*)?;?/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  /**
   * 分析现有主题配置
   */
  async analyzeCurrentTheme() {
    const theme = { ...this.defaultTheme };
    
    // 查找现有的主题文件
    const themeFiles = [
      path.join(this.projectPath, 'src/styles/element-variables.scss'),
      path.join(this.projectPath, 'src/styles/variables.scss'),
      path.join(this.projectPath, 'src/theme/index.scss')
    ];
    
    for (const themeFile of themeFiles) {
      if (await fs.pathExists(themeFile)) {
        const content = await fs.readFile(themeFile, 'utf8');
        this.extractThemeVariables(content, theme);
      }
    }
    
    return theme;
  }

  /**
   * 提取主题变量
   */
  extractThemeVariables(content, theme) {
    // 提取颜色变量
    const colorRegex = /\$--color-(\w+):\s*([^;]+);/g;
    let match;
    
    while ((match = colorRegex.exec(content)) !== null) {
      const colorType = match[1];
      const colorValue = match[2].trim();
      
      if (theme.colors[colorType]) {
        theme.colors[colorType].base = colorValue;
      }
    }
    
    // 提取字体大小变量
    const fontSizeRegex = /\$--font-size-(\w+):\s*([^;]+);/g;
    while ((match = fontSizeRegex.exec(content)) !== null) {
      const sizeType = match[1];
      const sizeValue = match[2].trim();
      
      if (theme.fontSize[sizeType] !== undefined) {
        theme.fontSize[sizeType] = sizeValue;
      }
    }
    
    // 提取边框半径变量
    const borderRadiusRegex = /\$--border-radius-(\w+):\s*([^;]+);/g;
    while ((match = borderRadiusRegex.exec(content)) !== null) {
      const radiusType = match[1];
      const radiusValue = match[2].trim();
      
      if (theme.borderRadius[radiusType] !== undefined) {
        theme.borderRadius[radiusType] = radiusValue;
      }
    }
  }

  /**
   * 生成 Element Plus 配置
   */
  async generateElementPlusConfig(theme) {
    const elementDir = path.join(this.projectPath, this.options.elementDir);
    await fs.ensureDir(elementDir);
    
    const configPath = path.join(elementDir, 'index.scss');
    
    let content = '// Element Plus 主题配置文件\n';
    content += '// 自动生成，用于替换 Element UI 主题导入\n\n';
    
    // 生成 @forward 配置
    content += '// 使用 @forward 配置 Element Plus 主题变量\n';
    content += '@forward \'element-plus/theme-chalk/src/common/var.scss\' with (\n';
    
    // 添加颜色配置
    content += '  $colors: (\n';
    for (const [colorType, colorConfig] of Object.entries(theme.colors)) {
      content += `    '${colorType}': (\n`;
      content += `      'base': ${colorConfig.base},\n`;
      content += '    ),\n';
    }
    content += '  ),\n\n';
    
    // 添加字体配置
    content += '  // 字体大小配置\n';
    content += `  $font-size-base: ${theme.fontSize.base},\n`;
    content += `  $font-size-small: ${theme.fontSize.small},\n`;
    content += `  $font-size-large: ${theme.fontSize.large},\n\n`;
    
    // 添加边框配置
    content += '  // 边框配置\n';
    content += `  $border-radius-base: ${theme.borderRadius.base},\n\n`;
    
    // 修复字体路径问题
    content += '  // 修复字体路径\n';
    content += '  $font-path: \'element-plus/dist/fonts\'\n';
    
    content += ');\n\n';
    
    // 可选：完整导入样式
    content += '// 可选：完整导入 Element Plus 样式\n';
    content += '// 推荐使用按需导入以减少包体积\n';
    content += '// @use "element-plus/theme-chalk/src/index.scss" as *;\n';
    
    await fs.writeFile(configPath, content, 'utf8');
    
    console.log(chalk.green(`✅ 生成 Element Plus 配置: ${path.relative(this.projectPath, configPath)}`));
  }

  /**
   * 更新导入语句
   */
  async updateImportStatements() {
    const sassFiles = await this.findSassFiles();
    let updatedFiles = 0;
    
    for (const filePath of sassFiles) {
      try {
        let content = await fs.readFile(filePath, 'utf8');
        let hasChanges = false;
        
        // 替换 Element UI 导入
        for (const [oldPath, newPath] of Object.entries(this.pathMappings)) {
          const oldImportRegex = new RegExp(`@import\\s+['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"](?:\\s*;)?`, 'g');
          
          if (oldImportRegex.test(content)) {
            // 替换为新的 @use 语句，指向我们的配置文件
            content = content.replace(oldImportRegex, '@use "src/styles/element" as *;');
            hasChanges = true;
          }
        }
        
        // 更新变量名
        for (const [oldVar, newVar] of Object.entries(this.variableMappings)) {
          // 转义特殊字符，特别是 $ 符号
          const escapedVar = oldVar.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
          const varRegex = new RegExp(escapedVar + '\\b', 'g');
          
          if (varRegex.test(content)) {
            content = content.replace(varRegex, newVar);
            hasChanges = true;
          }
        }
        
        if (hasChanges) {
          await fs.writeFile(filePath, content, 'utf8');
          updatedFiles++;
          
          if (this.options.verbose) {
            console.log(chalk.gray(`更新文件: ${path.relative(this.projectPath, filePath)}`));
          }
        }
      } catch (error) {
        console.error(chalk.red(`处理文件失败 ${filePath}: ${error.message}`));
        // 重新抛出错误以确保测试能够捕获
        throw error;
      }
    }
    
    if (updatedFiles > 0) {
      console.log(chalk.green(`✅ 更新了 ${updatedFiles} 个文件的导入语句`));
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport(usage, theme) {
    const reportPath = path.join(this.projectPath, 'element-plus-migration-report.md');
    
    let content = '# Element Plus 迁移报告\n\n';
    content += `生成时间: ${new Date().toLocaleString()}\n\n`;
    
    // 依赖信息
    content += '## 依赖信息\n\n';
    if (usage.hasElementUI) {
      content += `- Element UI 版本: ${usage.elementUIVersion}\n`;
    }
    if (usage.hasElementPlus) {
      content += `- Element Plus 版本: ${usage.elementPlusVersion}\n`;
    } else {
      content += '- ⚠️ 需要安装 Element Plus: `npm install element-plus`\n';
    }
    content += '\n';
    
    // 主题配置
    content += '## 主题配置\n\n';
    content += '已生成的主题配置文件: `src/styles/element/index.scss`\n\n';
    content += '### 主要颜色\n\n';
    for (const [colorType, colorConfig] of Object.entries(theme.colors)) {
      content += `- ${colorType}: ${colorConfig.base}\n`;
    }
    content += '\n';
    
    // 迁移建议
    content += '## 迁移建议\n\n';
    content += '1. 在主应用入口文件中导入新的主题配置:\n';
    content += '   ```javascript\n';
    content += '   import "src/styles/element/index.scss";\n';
    content += '   ```\n\n';
    content += '2. 移除旧的 Element UI 样式导入\n\n';
    content += '3. 更新组件导入方式（如果使用完整导入）:\n';
    content += '   ```javascript\n';
    content += '   // 旧方式\n';
    content += '   import ElementUI from "element-ui";\n';
    content += '   \n';
    content += '   // 新方式\n';
    content += '   import ElementPlus from "element-plus";\n';
    content += '   ```\n\n';
    
    // 注意事项
    content += '## 注意事项\n\n';
    content += '- Element Plus 的组件 API 可能与 Element UI 有所不同\n';
    content += '- 建议查阅 [Element Plus 官方文档](https://element-plus.org/) 了解详细变更\n';
    content += '- 测试所有使用 Element 组件的页面确保样式正确\n';
    
    await fs.writeFile(reportPath, content, 'utf8');
    
    console.log(chalk.blue(`📋 生成迁移报告: ${path.relative(this.projectPath, reportPath)}`));
    
    return {
      reportPath,
      updatedFiles: usage.importStatements.length,
      themeVariables: Object.keys(theme.colors).length
    };
  }
}

module.exports = ElementPlusMigrator;
