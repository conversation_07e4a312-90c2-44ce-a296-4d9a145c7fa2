const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 智能路径解析器
 * 处理 Sass 迁移中的各种路径解析问题
 */
class SassPathResolver {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.nodeModulesPath = path.join(this.projectPath, 'node_modules');
    this.options = {
      viteConfigPath: path.join(this.projectPath, 'vite.config.js'),
      verbose: false,
      ...options
    };
    
    this.viteConfig = null;
    this.packageJson = null;
    this.pathMappings = new Map();
    
    // 预定义的库路径映射
    this.libraryMappings = {
      'element-ui': {
        'packages/theme-chalk/src/index': 'element-plus/theme-chalk/src/index.scss',
        'lib/theme-chalk/index.css': 'element-plus/dist/index.css',
        'packages/theme-chalk/src/': 'element-plus/theme-chalk/src/'
      },
      'bootstrap': {
        'scss/bootstrap': 'bootstrap/scss/bootstrap'
      }
    };
  }

  /**
   * 初始化解析器
   */
  async initialize() {
    await this.loadViteConfig();
    await this.loadPackageJson();
    this.buildPathMappings();
  }

  /**
   * 加载 Vite 配置
   */
  async loadViteConfig() {
    try {
      if (await fs.pathExists(this.options.viteConfigPath)) {
        // 简单的配置文件解析（避免执行代码）
        const configContent = await fs.readFile(this.options.viteConfigPath, 'utf8');
        this.viteConfig = this.parseViteConfig(configContent);
        
        if (this.options.verbose) {
          console.log(chalk.gray(`已加载 Vite 配置: ${this.options.viteConfigPath}`));
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`无法加载 Vite 配置: ${error.message}`));
      }
    }
  }

  /**
   * 解析 Vite 配置文件内容
   */
  parseViteConfig(content) {
    const config = { resolve: { alias: {} }, css: { preprocessorOptions: { scss: {} } } };
    
    // 提取 alias 配置
    const aliasMatch = content.match(/alias\s*:\s*\{([^}]+)\}/s);
    if (aliasMatch) {
      const aliasContent = aliasMatch[1];
      const aliasRegex = /['"]([^'"]+)['"]\s*:\s*[^,\n]+/g;
      let match;
      while ((match = aliasRegex.exec(aliasContent)) !== null) {
        const aliasKey = match[1];
        // 简化处理，假设大多数别名指向 src 目录
        config.resolve.alias[aliasKey] = path.join(this.projectPath, 'src');
      }
    }
    
    // 提取 loadPaths 配置
    const loadPathsMatch = content.match(/loadPaths\s*:\s*\[([^\]]+)\]/);
    if (loadPathsMatch) {
      config.css.preprocessorOptions.scss.loadPaths = [this.nodeModulesPath];
    }
    
    return config;
  }

  /**
   * 加载 package.json
   */
  async loadPackageJson() {
    try {
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        this.packageJson = await fs.readJson(packageJsonPath);
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`无法加载 package.json: ${error.message}`));
      }
    }
  }

  /**
   * 构建路径映射
   */
  buildPathMappings() {
    // 添加 Vite 别名映射
    if (this.viteConfig?.resolve?.alias) {
      Object.entries(this.viteConfig.resolve.alias).forEach(([alias, target]) => {
        this.pathMappings.set(alias, target);
      });
    }
    
    // 添加默认映射
    this.pathMappings.set('@', path.join(this.projectPath, 'src'));
    this.pathMappings.set('~', this.nodeModulesPath);
  }

  /**
   * 解析导入路径
   */
  resolvePath(importPath, currentFilePath) {
    const originalPath = importPath;

    // 1. 处理库迁移（在处理 ~ 别名之前）
    const migratedPath = this.resolveLibraryMigration(importPath);
    if (migratedPath !== importPath) {
      // 如果发生了库迁移，直接返回迁移后的路径
      importPath = this.ensureExtension(migratedPath);
      if (this.options.verbose) {
        console.log(chalk.gray(`路径解析: ${originalPath} -> ${importPath}`));
      }
      return importPath;
    }

    // 2. 处理 ~ 别名
    if (importPath.startsWith('~')) {
      importPath = this.resolveTildeAlias(importPath);
    }

    // 3. 处理其他别名
    importPath = this.resolveAlias(importPath);

    // 4. 处理相对路径
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      importPath = this.resolveRelativePath(importPath, currentFilePath);
    }

    // 5. 确保文件扩展名
    importPath = this.ensureExtension(importPath);

    if (this.options.verbose && importPath !== originalPath) {
      console.log(chalk.gray(`路径解析: ${originalPath} -> ${importPath}`));
    }

    return importPath;
  }

  /**
   * 解析 ~ 别名
   */
  resolveTildeAlias(importPath) {
    if (importPath.startsWith('~')) {
      return path.join(this.nodeModulesPath, importPath.slice(1));
    }
    return importPath;
  }

  /**
   * 解析其他别名
   */
  resolveAlias(importPath) {
    for (const [alias, target] of this.pathMappings) {
      if (importPath.startsWith(alias + '/') || importPath === alias) {
        return importPath.replace(alias, target);
      }
    }
    return importPath;
  }

  /**
   * 解析库迁移
   */
  resolveLibraryMigration(importPath) {
    // 处理带 ~ 前缀的路径
    const cleanPath = importPath.startsWith('~') ? importPath.slice(1) : importPath;

    for (const [oldLib, mappings] of Object.entries(this.libraryMappings)) {
      if (cleanPath.includes(oldLib)) {
        for (const [oldPath, newPath] of Object.entries(mappings)) {
          if (cleanPath.includes(oldPath)) {
            // 如果是目录映射（以 / 结尾），需要保留后续路径
            if (oldPath.endsWith('/') && newPath.endsWith('/')) {
              const remainingPath = cleanPath.substring(cleanPath.indexOf(oldPath) + oldPath.length);
              return newPath + remainingPath;
            }
            return newPath;
          }
        }
      }
    }
    return importPath;
  }

  /**
   * 解析相对路径
   */
  resolveRelativePath(importPath, currentFilePath) {
    if (!currentFilePath) {
      return importPath;
    }

    const currentDir = path.dirname(currentFilePath);
    const resolved = path.resolve(currentDir, importPath);

    // 返回相对于项目根目录的路径
    return path.relative(this.projectPath, resolved);
  }

  /**
   * 确保文件扩展名
   */
  ensureExtension(importPath) {
    const ext = path.extname(importPath);
    if (!ext && !importPath.endsWith('/')) {
      // 尝试添加 .scss 扩展名
      const scssPath = importPath + '.scss';
      const sassPath = importPath + '.sass';
      
      // 这里可以添加文件存在性检查
      return scssPath;
    }
    return importPath;
  }

  /**
   * 转换 @import 为 @use
   */
  convertImportToUse(importStatement, currentFilePath) {
    // 匹配 @import 语句
    const importRegex = /@import\s+['"]([^'"]+)['"](?:\s*;)?/;
    const match = importStatement.match(importRegex);
    
    if (!match) {
      return importStatement;
    }
    
    const importPath = match[1];
    const resolvedPath = this.resolvePath(importPath, currentFilePath);
    
    // 生成命名空间
    const namespace = this.generateNamespace(resolvedPath);
    
    // 检查是否需要 as *
    const needsGlobalAccess = this.shouldUseGlobalAccess(importPath);
    
    if (needsGlobalAccess) {
      return `@use '${resolvedPath}' as *;`;
    } else {
      return `@use '${resolvedPath}' as ${namespace};`;
    }
  }

  /**
   * 生成命名空间
   */
  generateNamespace(importPath) {
    const basename = path.basename(importPath, path.extname(importPath));
    
    // 移除下划线前缀（Sass 部分文件约定）
    const cleanName = basename.startsWith('_') ? basename.slice(1) : basename;
    
    // 转换为有效的命名空间名称
    return cleanName.replace(/[^a-zA-Z0-9_-]/g, '-');
  }

  /**
   * 判断是否应该使用全局访问 (as *)
   */
  shouldUseGlobalAccess(importPath) {
    // 对于项目内部的工具文件，可以使用 as *
    const internalPatterns = [
      'variables',
      'mixins',
      'functions',
      'utils',
      'src/styles'
    ];

    // 对于 Element Plus 等外部库，也使用 as *
    const externalGlobalPatterns = [
      'element-plus',
      'element-ui'
    ];

    return internalPatterns.some(pattern => importPath.includes(pattern)) ||
           externalGlobalPatterns.some(pattern => importPath.includes(pattern));
  }

  /**
   * 检查路径是否存在
   */
  async pathExists(filePath) {
    try {
      return await fs.pathExists(filePath);
    } catch {
      return false;
    }
  }

  /**
   * 获取建议的 Vite 配置
   */
  getRecommendedViteConfig() {
    return {
      css: {
        preprocessorOptions: {
          scss: {
            loadPaths: [this.nodeModulesPath],
            // 可选：添加全局变量注入（谨慎使用）
            // additionalData: `@use 'src/styles' as *;`
          }
        }
      },
      resolve: {
        alias: {
          '@': path.join(this.projectPath, 'src')
        }
      }
    };
  }
}

module.exports = SassPathResolver;
