const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn } = require('child_process');

const SassPathResolver = require('./pathResolver');
const SassArchitectureRefactor = require('./architectureRefactor');
const ElementPlusMigrator = require('./elementPlusMigrator');
const SassErrorDiagnostic = require('./errorDiagnostic');
const ViteConfigOptimizer = require('../viteConfigOptimizer');

/**
 * 增强版 Sass 迁移器
 * 集成所有优化功能的完整 Sass 迁移解决方案
 */
class EnhancedSassMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      backup: true,
      verbose: false,
      dryRun: false,
      autoFix: true,
      include: ['**/*.scss', '**/*.sass'],
      exclude: ['node_modules/**', 'dist/**', 'build/**'],
      enableArchitectureRefactor: true,
      enableElementPlusMigration: true,
      enableViteOptimization: true,
      enableErrorDiagnostic: true,
      buildCommand: 'npm run build',
      ...options
    };

    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      skippedFiles: 0,
      errorFiles: 0,
      errors: [],
      optimizations: [],
      validationResults: null
    };

    // 初始化子模块
    this.pathResolver = new SassPathResolver(this.projectPath, options);
    this.architectureRefactor = new SassArchitectureRefactor(this.projectPath, options);
    this.elementPlusMigrator = new ElementPlusMigrator(this.projectPath, options);
    this.errorDiagnostic = new SassErrorDiagnostic(this.projectPath, options);
    this.viteOptimizer = new ViteConfigOptimizer(this.projectPath, options);
  }

  /**
   * 执行完整的 Sass 迁移
   */
  async migrate() {
    console.log(chalk.bold.blue('\n🎨 增强版 Sass 迁移工具'));
    console.log(chalk.gray(`项目路径: ${this.projectPath}`));

    if (this.options.dryRun) {
      console.log(chalk.yellow('🔍 运行在预览模式，不会修改文件'));
    }

    console.log('');

    try {
      // 阶段 1: 预检查和初始化
      await this.preCheck();

      // 阶段 2: Vite 配置优化
      if (this.options.enableViteOptimization) {
        await this.optimizeViteConfig();
      }

      // 阶段 3: Element Plus 迁移
      if (this.options.enableElementPlusMigration) {
        await this.migrateElementPlus();
      }

      // 阶段 4: 架构重构
      if (this.options.enableArchitectureRefactor) {
        await this.refactorArchitecture();
      }

      // 阶段 5: 基础 Sass 迁移
      await this.performBasicMigration();

      // 阶段 6: 错误诊断和修复
      if (this.options.enableErrorDiagnostic) {
        await this.diagnoseAndFix();
      }

      // 阶段 7: 验证和测试
      await this.validateMigration();

      // 生成最终报告
      const report = this.generateFinalReport();

      console.log('\n' + chalk.bold.green('✅ Sass 迁移完成!'));
      this.printFinalStats(report);

      return report;

    } catch (error) {
      console.error(chalk.red('\n❌ Sass 迁移失败:'));
      console.error(chalk.red(error.message));

      if (this.options.verbose) {
        console.error(error.stack);
      }

      throw error;
    }
  }

  /**
   * 预检查和初始化
   */
  async preCheck() {
    console.log(chalk.blue('🔍 阶段 1: 预检查和初始化...'));

    // 初始化路径解析器
    await this.pathResolver.initialize();

    // 检查项目结构
    await this.checkProjectStructure();

    // 查找 Sass 文件
    const sassFiles = await this.findSassFiles();
    this.stats.totalFiles = sassFiles.length;

    if (sassFiles.length === 0) {
      console.log(chalk.gray('未找到需要迁移的 Sass/SCSS 文件'));
      return;
    }

    console.log(chalk.green(`✅ 找到 ${sassFiles.length} 个 Sass/SCSS 文件`));
  }

  /**
   * 检查项目结构
   */
  async checkProjectStructure() {
    const packageJsonPath = path.join(this.projectPath, 'package.json');

    if (!(await fs.pathExists(packageJsonPath))) {
      throw new Error('未找到 package.json 文件，请确认项目路径正确');
    }

    const packageJson = await fs.readJson(packageJsonPath);

    // 检查是否是 Vue 项目
    const isVueProject = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
    if (!isVueProject) {
      console.log(chalk.yellow('⚠️  未检测到 Vue 依赖，某些功能可能不适用'));
    }

    // 检查是否使用 Vite
    const isViteProject = packageJson.devDependencies?.vite || packageJson.dependencies?.vite;
    if (!isViteProject) {
      console.log(chalk.yellow('⚠️  未检测到 Vite，某些配置优化可能不适用'));
    }
  }

  /**
   * 优化 Vite 配置
   */
  async optimizeViteConfig() {
    console.log(chalk.blue('⚙️  阶段 2: Vite 配置优化...'));

    try {
      const result = await this.viteOptimizer.optimize();
      this.stats.optimizations.push(...result.optimizations);

      if (result.optimizations.length > 0) {
        console.log(chalk.green(`✅ 应用了 ${result.optimizations.length} 项 Vite 配置优化`));
      } else {
        console.log(chalk.gray('Vite 配置已是最优状态'));
      }
    } catch (error) {
      console.warn(chalk.yellow(`Vite 配置优化失败: ${error.message}`));
    }
  }

  /**
   * Element Plus 迁移
   */
  async migrateElementPlus() {
    console.log(chalk.blue('🎨 阶段 3: Element Plus 主题迁移...'));

    try {
      const result = await this.elementPlusMigrator.migrate();

      if (result.skipped) {
        console.log(chalk.gray('未检测到 Element UI，跳过主题迁移'));
      } else {
        console.log(chalk.green(`✅ Element Plus 主题迁移完成`));
        this.stats.optimizations.push({
          type: 'element-plus',
          description: 'Element Plus 主题迁移',
          details: result
        });
      }
    } catch (error) {
      console.warn(chalk.yellow(`Element Plus 迁移失败: ${error.message}`));
    }
  }

  /**
   * 架构重构
   */
  async refactorArchitecture() {
    console.log(chalk.blue('🏗️  阶段 4: 架构重构...'));

    try {
      const result = await this.architectureRefactor.refactor();

      console.log(chalk.green(`✅ 架构重构完成`));
      console.log(chalk.gray(`  - 变量: ${result.variablesCount}`));
      console.log(chalk.gray(`  - 混入: ${result.mixinsCount}`));
      console.log(chalk.gray(`  - 函数: ${result.functionsCount}`));

      if (result.circularDependencies.length > 0) {
        console.log(chalk.yellow(`  - 发现 ${result.circularDependencies.length} 个循环依赖`));
      }

      this.stats.optimizations.push({
        type: 'architecture',
        description: '架构重构',
        details: result
      });
    } catch (error) {
      console.warn(chalk.yellow(`架构重构失败: ${error.message}`));
    }
  }

  /**
   * 执行基础 Sass 迁移
   */
  async performBasicMigration() {
    console.log(chalk.blue('🔄 阶段 5: 基础 Sass 语法迁移...'));

    // 检查 sass-migrator 是否可用
    if (!(await this.isSassMigratorAvailable())) {
      console.log(chalk.yellow('⚠️  sass-migrator 不可用，使用内置迁移逻辑'));
      await this.performInternalMigration();
    } else {
      await this.performExternalMigration();
    }
  }

  /**
   * 检查 sass-migrator 是否可用
   */
  async isSassMigratorAvailable() {
    try {
      const { execSync } = require('child_process');
      execSync('sass-migrator --version', { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 使用内置逻辑进行迁移
   */
  async performInternalMigration() {
    const sassFiles = await this.findSassFiles();

    for (const filePath of sassFiles) {
      await this.migrateFileInternal(filePath);
    }
  }

  /**
   * 使用内置逻辑迁移单个文件
   */
  async migrateFileInternal(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const relativePath = path.relative(this.projectPath, filePath);

      // 检查是否需要迁移
      if (!this.needsMigration(content)) {
        this.stats.skippedFiles++;
        return;
      }

      console.log(chalk.gray(`  处理文件: ${relativePath}`));

      // 备份文件
      if (this.options.backup && !this.options.dryRun) {
        await this.backupFile(filePath);
      }

      // 转换内容
      let newContent = content;

      // 转换 @import 为 @use
      newContent = this.convertImportsToUse(newContent, filePath);

      // 解析和修复路径
      newContent = this.fixPaths(newContent, filePath);

      // 写入文件
      if (!this.options.dryRun) {
        await fs.writeFile(filePath, newContent, 'utf8');
      }

      console.log(chalk.green(`    ✅ 迁移成功`));
      this.stats.processedFiles++;

    } catch (error) {
      console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`));
      this.stats.errorFiles++;
      this.stats.errors.push({
        file: filePath,
        error: error.message
      });
    }
  }

  /**
   * 检查文件是否需要迁移
   */
  needsMigration(content) {
    return /@import\s+['"][^'"]+['"];?/.test(content);
  }

  /**
   * 转换 @import 为 @use
   */
  convertImportsToUse(content, filePath) {
    return content.replace(/@import\s+['"]([^'"]+)['"](?:\s*;)?/g, (match, importPath) => {
      return this.pathResolver.convertImportToUse(match, filePath);
    });
  }

  /**
   * 修复路径
   */
  fixPaths(content, filePath) {
    return content.replace(/@(?:use|forward)\s+['"]([^'"]+)['"]/g, (match, importPath) => {
      const resolvedPath = this.pathResolver.resolvePath(importPath, filePath);
      return match.replace(importPath, resolvedPath);
    });
  }

  /**
   * 使用外部 sass-migrator 进行迁移
   */
  async performExternalMigration() {
    const sassFiles = await this.findSassFiles();

    for (const filePath of sassFiles) {
      await this.migrateFileExternal(filePath);
    }
  }

  /**
   * 使用外部工具迁移单个文件
   */
  async migrateFileExternal(filePath) {
    try {
      const relativePath = path.relative(this.projectPath, filePath);
      console.log(chalk.gray(`  处理文件: ${relativePath}`));

      // 检查是否需要迁移
      const content = await fs.readFile(filePath, 'utf8');
      if (!this.needsMigration(content)) {
        this.stats.skippedFiles++;
        return;
      }

      // 备份文件
      if (this.options.backup && !this.options.dryRun) {
        await this.backupFile(filePath);
      }

      // 构建命令
      const args = ['module', filePath];
      if (this.options.dryRun) {
        args.push('--dry-run');
      }

      // 执行迁移
      await this.executeSassMigrator(args);

      console.log(chalk.green(`    ✅ 迁移成功`));
      this.stats.processedFiles++;

    } catch (error) {
      console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`));
      this.stats.errorFiles++;
      this.stats.errors.push({
        file: filePath,
        error: error.message
      });
    }
  }

  /**
   * 执行 sass-migrator 命令
   */
  async executeSassMigrator(args) {
    return new Promise((resolve, reject) => {
      const child = spawn('sass-migrator', args, {
        cwd: this.projectPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(stderr || `sass-migrator exited with code ${code}`));
        }
      });

      child.on('error', (error) => {
        reject(new Error(`Failed to start sass-migrator: ${error.message}`));
      });
    });
  }

  /**
   * 备份文件
   */
  async backupFile(filePath) {
    const backupPath = `${filePath}.sass-backup`;
    await fs.copy(filePath, backupPath);
  }

  /**
   * 错误诊断和修复
   */
  async diagnoseAndFix() {
    console.log(chalk.blue('🔍 阶段 6: 错误诊断和修复...'));

    try {
      // 尝试构建项目以获取错误信息
      const buildErrors = await this.tryBuild();

      // 执行诊断
      const diagnostic = await this.errorDiagnostic.diagnose(buildErrors);

      if (diagnostic.totalIssues > 0) {
        console.log(chalk.yellow(`发现 ${diagnostic.totalIssues} 个问题`));

        if (this.options.verbose) {
          this.errorDiagnostic.printDiagnosticResults(diagnostic);
        }
      } else {
        console.log(chalk.green('✅ 未发现问题'));
      }

      this.stats.validationResults = diagnostic;

    } catch (error) {
      console.warn(chalk.yellow(`错误诊断失败: ${error.message}`));
    }
  }

  /**
   * 尝试构建项目
   */
  async tryBuild() {
    try {
      const { execSync } = require('child_process');
      execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        stdio: 'pipe',
        timeout: 60000
      });
      return '';
    } catch (error) {
      return error.stderr?.toString() || error.stdout?.toString() || '';
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log(chalk.blue('✅ 阶段 7: 验证迁移结果...'));

    // 检查是否还有 @import 语句
    const remainingImports = await this.checkRemainingImports();

    if (remainingImports.length > 0) {
      console.log(chalk.yellow(`⚠️  仍有 ${remainingImports.length} 个文件包含 @import 语句`));

      if (this.options.verbose) {
        remainingImports.forEach(file => {
          console.log(chalk.gray(`  - ${file}`));
        });
      }
    } else {
      console.log(chalk.green('✅ 所有 @import 语句已成功迁移'));
    }

    // 尝试最终构建
    console.log(chalk.gray('尝试构建项目验证迁移结果...'));
    const buildResult = await this.tryBuild();

    if (!buildResult) {
      console.log(chalk.green('✅ 项目构建成功'));
    } else {
      console.log(chalk.yellow('⚠️  构建过程中发现问题，请检查错误信息'));
    }
  }

  /**
   * 检查剩余的 @import 语句
   */
  async checkRemainingImports() {
    const sassFiles = await this.findSassFiles();
    const filesWithImports = [];

    for (const filePath of sassFiles) {
      const content = await fs.readFile(filePath, 'utf8');
      if (this.needsMigration(content)) {
        filesWithImports.push(path.relative(this.projectPath, filePath));
      }
    }

    return filesWithImports;
  }

  /**
   * 查找 Sass 文件
   */
  async findSassFiles() {
    const { glob } = require('glob');
    const allFiles = [];

    for (const pattern of this.options.include) {
      const files = await glob(pattern, {
        cwd: this.projectPath,
        absolute: true,
        ignore: this.options.exclude
      });
      allFiles.push(...files);
    }

    return [...new Set(allFiles)];
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    return {
      summary: {
        totalFiles: this.stats.totalFiles,
        processedFiles: this.stats.processedFiles,
        skippedFiles: this.stats.skippedFiles,
        errorFiles: this.stats.errorFiles
      },
      optimizations: this.stats.optimizations,
      validation: this.stats.validationResults,
      errors: this.stats.errors,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 打印最终统计信息
   */
  printFinalStats(report) {
    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`总文件数: ${report.summary.totalFiles}`);
    console.log(`已处理: ${chalk.green(report.summary.processedFiles)}`);
    console.log(`已跳过: ${chalk.gray(report.summary.skippedFiles)}`);
    console.log(`错误: ${chalk.red(report.summary.errorFiles)}`);
    console.log(`优化项: ${chalk.blue(report.optimizations.length)}`);

    if (report.errors.length > 0) {
      console.log('\n' + chalk.bold.red('❌ 错误详情:'));
      report.errors.forEach(({ file, error }) => {
        console.log(chalk.red(`  ${path.relative(this.projectPath, file)}: ${error}`));
      });
    }

    if (report.optimizations.length > 0) {
      console.log('\n' + chalk.bold.blue('🚀 应用的优化:'));
      report.optimizations.forEach(opt => {
        console.log(chalk.blue(`  ✓ ${opt.description || opt.title}`));
      });
    }

    console.log('\n' + chalk.bold.green('🎉 迁移完成！'));
    console.log(chalk.gray('建议运行项目构建测试以确保一切正常工作。'));
  }
}

module.exports = EnhancedSassMigrator;
