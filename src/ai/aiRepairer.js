const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('./ai-service');

/**
 * AI 代码修复器
 * 继承自 AIService，专门用于修复 Vue 2 到 Vue 3 迁移失败的文件
 */
class AIRepairer extends AIService {
  constructor(options = {}) {
    super(options);

    if (this.enabled) {
      console.log(chalk.green(`✅ AI 修复功能已启用 (${this.llmConfig.providerName})`));
    }
  }

  /**
   * 修复失败的文件列表
   */
  async repairFailedFiles(failedFiles, projectPath) {
    if (!this.enabled) {
      console.log(chalk.yellow('⚠️  AI 修复功能未启用，跳过修复步骤'));
      return { success: false, reason: 'AI repair disabled' };
    }

    console.log(chalk.blue(`🤖 开始使用 AI 修复 ${failedFiles.length} 个失败文件...`));

    const results = [];

    for (const failedFile of failedFiles) {
      try {
        const result = await this.repairSingleFile(failedFile, projectPath);
        results.push(result);

        if (result.success) {
          this.stats.success++;
        } else {
          this.stats.failed++;
        }
      } catch (error) {
        console.error(chalk.red(`❌ 修复文件失败: ${failedFile.file}`), error.message);
        this.stats.failed++;
        results.push({
          file: failedFile.file,
          success: false,
          error: error.message
        });
      }

      this.stats.attempted++;
    }

    this.printRepairStats();
    return results;
  }

  /**
   * 修复单个文件
   */
  async repairSingleFile(failedFile, projectPath) {
    const filePath = path.isAbsolute(failedFile.absolutePath)
      ? failedFile.absolutePath
      : path.join(projectPath, failedFile.file);

    console.log(chalk.gray(`🔧 修复: ${failedFile.file}...`));

    try {
      // 读取原始文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');

      // 生成修复提示
      const prompt = this.generateRepairPrompt(originalContent, failedFile);

      // 调用 AI 进行修复
      const repairedContent = await this.callAI(prompt);

      // 验证修复结果
      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // 备份原文件
        await this.backupFile(filePath);

        // 写入修复后的内容
        await fs.writeFile(filePath, repairedContent, 'utf8');

        console.log(chalk.green(`✅ 修复成功: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  修复结果验证失败: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (error) {
      console.log(chalk.red(`❌ 修复失败: ${failedFile.file}`));
      return {
        file: failedFile.file,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成修复提示
   */
  generateRepairPrompt(originalContent, failedFile) {
    const fileExtension = path.extname(failedFile.file);
    const errorMessage = failedFile.error;

    // 检查是否是 Sass 文件
    if (fileExtension === '.scss' || fileExtension === '.sass') {
      return this.generateSassRepairPrompt(originalContent, failedFile);
    }

    let prompt = `将如下的 Vue 2 + Element UI 组件代码，迁移到 Vue 3 + Element Plus 组件代码

**迁移要求**:
1. 状态管理：如果原来的代码使用 Vuex 状态管理，请转换为 Vuex 4.x 语法，如果没有用到状态管理就不要添加；
2. 功能复刻：不要求实现一样，但是输入参数必须一致；
3. Vue 3 语法：使用 Vue 3 的 Composition API 或 Options API；
4. Element Plus：将 Element UI 组件替换为 Element Plus 组件；
5. 事件处理：更新事件处理方式以兼容 Vue 3；
6. 响应式数据：使用 Vue 3 的响应式 API；

**原始错误**: ${errorMessage}

**原始代码**:
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

请使用 ${fileExtension.slice(1)} 代码块返回修复后的完整代码，方便我直接使用。`;

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定要求**:
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 在 <script> 中使用 defineComponent 或 Composition API
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript 文件特定要求**:
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句`;
    }

    return prompt;
  }

  /**
   * 生成 Sass 文件修复提示
   */
  generateSassRepairPrompt(originalContent, failedFile) {
    const fileExtension = path.extname(failedFile.file);
    const errorMessage = failedFile.error;

    let prompt = `请修复以下 Sass/SCSS 文件，将其从 Element UI 迁移到 Element Plus，并解决导入路径问题。

**修复要求**:
1. 将 Element UI 相关的导入路径更新为 Element Plus
2. 修复无法找到的 Sass 文件路径
3. 将 @import 语法迁移为 @use 语法（如果可能）
4. 保持所有变量定义和样式规则不变
5. 确保路径解析正确

**常见路径映射**:
- \`~element-ui/packages/theme-chalk/src/index\` → \`element-plus/theme-chalk/src/index\`
- \`~element-ui/lib/theme-chalk/fonts\` → \`element-plus/lib/theme-chalk/fonts\`
- \`~element-ui/\` → \`element-plus/\`

**原始错误**: ${errorMessage}

**原始代码**:
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

**修复指导**:`;

    // 根据错误类型提供特定指导
    if (errorMessage.includes('Could not find Sass file')) {
      prompt += `
- 这是一个路径解析错误，需要更新导入路径
- 检查 node_modules 中是否存在对应的文件
- 将 element-ui 路径替换为 element-plus 路径`;
    }

    if (errorMessage.includes('element-ui')) {
      prompt += `
- 将所有 element-ui 相关路径替换为 element-plus
- 注意 Element Plus 的目录结构可能与 Element UI 不同`;
    }

    prompt += `

请返回修复后的完整 ${fileExtension.slice(1)} 代码，确保：
1. 所有导入路径都是有效的
2. 保持原有的变量定义和样式
3. 使用正确的 Element Plus 路径`;

    return prompt;
  }





  /**
   * 打印修复统计
   */
  printRepairStats() {
    console.log('\n' + chalk.bold('🤖 AI 修复统计:'));
    console.log(`尝试修复: ${this.stats.attempted} 个文件`);
    console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));

    if (this.stats.attempted > 0) {
      const successRate = ((this.stats.success / this.stats.attempted) * 100).toFixed(1);
      console.log(chalk.bold(`成功率: ${successRate}%`));
    }
  }


}

module.exports = AIRepairer;
