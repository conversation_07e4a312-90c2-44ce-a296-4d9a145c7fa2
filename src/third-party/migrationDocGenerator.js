const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { getMigratorDocPath, getMigratorDocContent, getAllMigratorDocs } = require('./migrator-docs');

/**
 * 迁移文档生成器
 * 根据 package.json 分析结果和 migrator/docs 下的组件文档生成详细的迁移指导文档
 */
class MigrationDocGenerator {
  constructor(projectPath, analysisResult, options = {}) {
    this.projectPath = projectPath;
    this.analysisResult = analysisResult || {};
    this.options = {
      outputPath: options.outputPath || path.join(projectPath, 'migration-report.md'),
      includeUsageDetails: options.includeUsageDetails !== false,
      language: options.language || 'zh-CN',
      ...options
    };

    // 如果没有分析结果，从 package.json 生成
    if (!this.analysisResult.dependencies) {
      this.analysisResult = this.generateAnalysisFromPackageJson();
    }
  }

  /**
   * 从 package.json 生成分析结果
   */
  generateAnalysisFromPackageJson() {
    try {
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!fs.existsSync(packageJsonPath)) {
        return { dependencies: [], summary: { totalDependencies: 0, dependenciesWithDocs: 0, dependenciesWithoutDocs: 0 } };
      }

      const packageJson = fs.readJsonSync(packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      const dependencies = [];
      let dependenciesWithDocs = 0;

      for (const [name, version] of Object.entries(allDeps)) {
        const hasDoc = !!getMigratorDocPath(name);
        if (hasDoc) dependenciesWithDocs++;

        dependencies.push({
          name,
          version,
          hasDoc,
          category: this.categorizeDependency(name)
        });
      }

      return {
        dependencies,
        summary: {
          totalDependencies: dependencies.length,
          dependenciesWithDocs,
          dependenciesWithoutDocs: dependencies.length - dependenciesWithDocs,
          filesScanned: 0,
          usageFound: 0
        },
        usageInCode: []
      };
    } catch (error) {
      console.warn(chalk.yellow('⚠️  读取 package.json 失败:'), error.message);
      return { dependencies: [], summary: { totalDependencies: 0, dependenciesWithDocs: 0, dependenciesWithoutDocs: 0 }, usageInCode: [] };
    }
  }

  /**
   * 分类依赖
   */
  categorizeDependency(name) {
    if (name.includes('vue') && (name.includes('router') || name.includes('vuex'))) {
      return 'vue-core';
    }
    if (name.includes('element') || name.includes('ant-design') || name.includes('vuetify')) {
      return 'ui-library';
    }
    if (name.includes('chart') || name.includes('echarts') || name.includes('d3')) {
      return 'chart';
    }
    if (name.includes('editor') || name.includes('tinymce') || name.includes('codemirror')) {
      return 'editor';
    }
    if (name.includes('vue-') || name.includes('@vue/')) {
      return 'utility';
    }
    return 'other';
  }

  /**
   * 生成迁移指导文档
   */
  async generateMigrationGuide() {
    try {
      const content = this.buildDocumentContent();
      
      await fs.writeFile(this.options.outputPath, content, 'utf8');
      
      return {
        success: true,
        outputPath: this.options.outputPath,
        contentLength: content.length
      };
    } catch (error) {
      console.error(chalk.red('❌ 生成迁移文档失败:'), error.message);
      throw error;
    }
  }

  /**
   * 构建文档内容
   */
  buildDocumentContent() {
    const sections = [
      this.buildHeader(),
      this.buildSummary(),
      this.buildAvailableMigrationDocs(),
      this.buildDependencyMigrationGuide(),
      this.buildCodeMigrationGuide(),
      this.buildStepByStepGuide(),
      this.buildTroubleshooting(),
      this.buildResources(),
      this.buildFooter()
    ];

    return sections.join('\n\n');
  }

  /**
   * 构建可用迁移文档概览
   */
  buildAvailableMigrationDocs() {
    const allDocs = getAllMigratorDocs();

    if (allDocs.length === 0) {
      return `## 📚 可用迁移文档

暂无可用的迁移文档。`;
    }

    let section = `## 📚 可用迁移文档

本工具包含以下组件的详细迁移指导文档：

`;

    allDocs.forEach(doc => {
      const docContent = getMigratorDocContent(doc.name);
      let title = doc.name;

      // 尝试从文档内容中提取标题
      if (docContent) {
        const titleMatch = docContent.match(/^---\s*\ntitle:\s*['"]?([^'"]+)['"]?\s*\n/m);
        if (titleMatch) {
          title = titleMatch[1];
        }
      }

      section += `- **${doc.name}**: ${title}\n`;
    });

    section += `\n总计 ${allDocs.length} 个组件的迁移文档可用。`;

    return section;
  }

  /**
   * 构建文档头部
   */
  buildHeader() {
    const projectName = path.basename(this.projectPath);
    const timestamp = new Date().toLocaleString('zh-CN');

    return `# Vue 2 到 Vue 3 迁移指导文档

> 项目: ${projectName}  
> 生成时间: ${timestamp}  
> 工具版本: vue-migrator v1.0.0

本文档基于项目分析结果自动生成，提供详细的 Vue 2 到 Vue 3 迁移指导。

## 📋 目录

- [迁移概览](#迁移概览)
- [依赖迁移指南](#依赖迁移指南)
- [代码迁移指南](#代码迁移指南)
- [分步迁移指南](#分步迁移指南)
- [常见问题](#常见问题)
- [参考资源](#参考资源)`;
  }

  /**
   * 构建迁移概览
   */
  buildSummary() {
    const { summary } = this.analysisResult;
    
    return `## 📊 迁移概览

**依赖总数**: ${summary.totalDependencies}
**有迁移文档的依赖**: ${summary.dependenciesWithDocs}
**无迁移文档的依赖**: ${summary.dependenciesWithoutDocs}

${this.getMigrationOverview()}

该迁移将涉及以下多个方面:
* Vue 核心库升级 (Vue 2.x → Vue 3.x)
* Vue Router 和 Vuex 升级
* UI 组件库升级或替换
* 代码结构和语法调整
`;
  }

  /**
   * 获取迁移概要信息
   */
  getMigrationOverview() {
    return `**提示**: 迁移时间因项目复杂度、团队经验和依赖兼容性而异。建议先仔细评估项目特点再制定详细的迁移计划。`;
  }

  /**
   * 构建依赖迁移指南
   */
  buildDependencyMigrationGuide() {
    let content = `## 📦 依赖迁移指南

### 需要迁移的依赖

以下是检测到的需要迁移的依赖及其处理方案：

`;

    // 按类别分组依赖
    const dependenciesByCategory = this.groupDependenciesByCategory();

    for (const [category, deps] of Object.entries(dependenciesByCategory)) {
      content += `#### ${this.getCategoryDisplayName(category)}\n\n`;

      // 分离有文档和无文档的依赖
      const depsWithDocs = deps.filter(dep => dep.hasDoc);
      const depsWithoutDocs = deps.filter(dep => !dep.hasDoc);

      // 先显示有文档的依赖（详细信息）
      for (const dep of depsWithDocs) {
        content += this.buildDependencyMigrationSection(dep);
      }

      // 然后合并显示无文档的依赖
      if (depsWithoutDocs.length > 0) {
        content += this.buildNoDependenciesSummary(depsWithoutDocs, category);
      }

      content += '\n';
    }

    return content;
  }

  /**
   * 构建无迁移文档依赖的汇总
   */
  buildNoDependenciesSummary(dependencies, category) {
    if (dependencies.length === 0) return '';

    let section = `##### 📋 其他${this.getCategoryDisplayName(category).replace(/[🔧🎨📊✏️🛠️📋]/g, '')}依赖\n\n`;
    section += `以下 ${dependencies.length} 个依赖暂无专门的迁移文档，请参考通用迁移建议：\n\n`;

    // 列出所有无文档的依赖
    dependencies.forEach(dep => {
      section += `- **${dep.name}** (${dep.version})\n`;
    });

    section += '\n';
    section += this.generateGenericMigrationAdvice({ category });

    return section;
  }

  /**
   * 按类别分组依赖
   */
  groupDependenciesByCategory() {
    const groups = {};
    
    for (const dep of this.analysisResult.dependencies) {
      if (!groups[dep.category]) {
        groups[dep.category] = [];
      }
      groups[dep.category].push(dep);
    }
    
    return groups;
  }

  /**
   * 获取类别显示名称
   */
  getCategoryDisplayName(category) {
    const names = {
      'vue-core': '🔧 Vue 核心',
      'ui-library': '🎨 UI 组件库',
      'chart': '📊 图表组件',
      'editor': '✏️ 编辑器组件',
      'utility': '🛠️ 工具组件',
      'other': '📋 其他组件'
    };
    
    return names[category] || '📋 其他组件';
  }

  /**
   * 构建单个依赖的迁移部分（仅用于有文档的依赖）
   */
  buildDependencyMigrationSection(dependency) {
    if (!dependency.hasDoc) return '';

    const usageLocations = this.getUsageLocations(dependency.name);

    let section = `##### ${dependency.name}\n\n`;
    section += `- **当前版本**: ${dependency.version}\n`;
    section += `- **使用位置**: ${usageLocations.length} 处\n\n`;

    // 读取并提取迁移文档的关键信息
    const docContent = getMigratorDocContent(dependency.name);
    if (docContent) {
      const migrationSummary = this.extractMigrationSummary(docContent, dependency.name);
      section += `**迁移指导**:\n\n`;
      section += `${migrationSummary}\n\n`;
    } else {
      section += `**迁移指导**: 请参考 \`migrate-cli/src/migrator/docs/${dependency.name}.md\`\n\n`;
    }

    return section;
  }

  /**
   * 提取迁移文档的关键信息摘要
   */
  extractMigrationSummary(docContent, packageName) {
    try {
      // 提取 frontmatter 信息
      const frontmatterMatch = docContent.match(/^---\s*\n([\s\S]*?)\n---/);
      let target = '';
      let link = '';

      if (frontmatterMatch) {
        const frontmatter = frontmatterMatch[1];
        const targetMatch = frontmatter.match(/target:\s*(.+)/);
        const linkMatch = frontmatter.match(/link:\s*(.+)/);

        if (targetMatch) target = targetMatch[1].trim();
        if (linkMatch) link = linkMatch[1].trim();
      }

      // 提取主要变化或安装信息
      const installMatch = docContent.match(/```bash\s*\n(npm uninstall[^`]+npm install[^`]+)\n```/);
      const changesMatch = docContent.match(/## 主要(变化|区别|差异)\s*\n\n([\s\S]*?)(?=\n##|\n---|\n```|$)/);

      let summary = '';

      if (target) {
        summary += `**目标包**: \`${target}\`\n`;
      }

      if (installMatch) {
        const installCommands = installMatch[1].split('\n').map(cmd => cmd.trim()).filter(cmd => cmd);
        summary += `**安装命令**:\n\`\`\`bash\n${installCommands.join('\n')}\n\`\`\`\n`;
      }

      if (changesMatch) {
        const changes = changesMatch[2].trim();
        // 提取前3个要点
        const points = changes.split(/\n\s*[-*]\s*/).slice(0, 4).map(point => point.trim()).filter(point => point);
        if (points.length > 0) {
          summary += `**主要变化**:\n`;
          points.forEach(point => {
            const cleanPoint = point.replace(/\*\*(.*?)\*\*/g, '$1').substring(0, 100);
            summary += `- ${cleanPoint}${cleanPoint.length >= 100 ? '...' : ''}\n`;
          });
        }
      }

      if (link) {
        summary += `\n📖 [详细文档](${link})`;
      }

      return summary || `请参考 \`migrate-cli/src/migrator/docs/${packageName}.md\` 获取详细迁移指导。`;

    } catch (error) {
      return `请参考 \`migrate-cli/src/migrator/docs/${packageName}.md\` 获取详细迁移指导。`;
    }
  }

  /**
   * 生成通用迁移建议
   */
  generateGenericMigrationAdvice(dependency) {
    let advice = `**建议操作**:\n`;
    
    switch (dependency.category) {
      case 'vue-core':
        advice += `1. 检查 [Vue 3 官方迁移指南](https://v3-migration.vuejs.org/)\n`;
        advice += `2. 更新到 Vue 3 兼容版本\n`;
        advice += `3. 使用 Vue 3 迁移构建版本进行渐进式迁移\n`;
        break;
      case 'ui-library':
        advice += `1. 查找该组件库的 Vue 3 版本\n`;
        advice += `2. 如果没有 Vue 3 版本，寻找替代方案\n`;
        advice += `3. 更新组件的导入和使用方式\n`;
        break;
      default:
        advice += `1. 访问 [${dependency.name} 官方文档](https://www.npmjs.com/package/${dependency.name})\n`;
        advice += `2. 检查是否支持 Vue 3\n`;
        advice += `3. 如不支持，寻找 Vue 3 兼容的替代方案\n`;
    }
    
    advice += '\n';
    return advice;
  }

  /**
   * 构建代码迁移指南
   */
  buildCodeMigrationGuide() {
    return `## 💻 代码迁移指南

### 核心变更

1. **应用创建**: \`${'new Vue()'}\` → \`${'createApp()'}\`
2. **全局API**: \`${'Vue.use()'}\` → \`${'app.use()'}\`
3. **生命周期**: \`${'beforeDestroy/destroyed'}\` → \`${'beforeUnmount/unmounted'}\`
4. **事件系统**: 移除 \`${'$on/$off'}\`，使用事件总线或状态管理
5. **组合式API**: 推荐使用 \`${'<script setup>'}\` 语法

### 关键代码示例

\`\`\`javascript
// Vue 2 → Vue 3 应用创建
- ${'new Vue({ render: h => h(App) }).$mount(\'#app\')'}
+ ${'createApp(App).mount(\'#app\')'}
- new Vue({ render: h => h(App) }).$mount('#app')
+ createApp(App).mount('#app')

// 插件注册
- Vue.use(SomePlugin)
+ app.use(SomePlugin)

// 生命周期钩子
- beforeDestroy() { }
+ beforeUnmount() { }
\`\`\`

💡 **建议**: 优先处理有迁移文档的依赖，然后逐步更新核心代码。`;
  }

  /**
   * 获取依赖的使用位置
   */
  getUsageLocations(dependencyName) {
    return this.analysisResult.usageInCode.filter(usage => 
      usage.dependency === dependencyName
    );
  }

  /**
   * 构建分步迁移指南
   */
  buildStepByStepGuide() {
    return `## 🚀 迁移步骤

### 1. 准备阶段
- 创建迁移分支: \`git checkout -b vue3-migration\`
- 分析依赖兼容性（已完成）
- 制定迁移计划

### 2. 核心升级
\`\`\`bash
npm install vue@next vue-router@4
npm install @vue/compat  # 兼容模式
\`\`\`

### 3. 依赖迁移
- 按本报告的依赖指南逐个迁移
- 优先处理有详细文档的依赖
- Element UI → Element Plus

### 4. 代码迁移
- 从叶子组件开始迁移
- 更新生命周期钩子
- 替换事件总线

### 5. 测试验证
- 功能测试
- 性能验证
- 修复问题`;
  }

  /**
   * 构建故障排除部分
   */
  buildTroubleshooting() {
    return `## 🔧 常见问题

- **构建错误**: 检查 Vue 3 版本和构建配置
- **$on/$off 报错**: 使用事件总线库（如 mitt）替代
- **Element UI 不工作**: 迁移到 Element Plus
- **生命周期报错**: 更新钩子名称（beforeDestroy → beforeUnmount）
- **性能问题**: 检查响应式 API 使用`;
  }

  /**
   * 构建参考资源部分
   */
  buildResources() {
    return `## 📚 参考资源

- [Vue 3 迁移指南](https://v3-migration.vuejs.org/) - 官方迁移文档
- [Element Plus](https://element-plus.org/) - Vue 3 UI 组件库
- [Vue Router 4](https://router.vuejs.org/) - Vue 3 路由
- [Pinia](https://pinia.vuejs.org/) - Vue 3 状态管理`;
  }

  /**
   * 构建文档尾部
   */
  buildFooter() {
    return `---

*本文档由 vue-migrator 工具自动生成。如有问题，请参考官方文档或寻求社区帮助。*

**祝您迁移顺利！** 🎉`;
  }
}

module.exports = MigrationDocGenerator;
