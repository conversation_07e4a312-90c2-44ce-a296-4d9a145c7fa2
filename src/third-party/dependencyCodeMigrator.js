const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const glob = require('glob');
const { AIService } = require('../ai/ai-service');

/**
 * 依赖代码迁移器
 * 找到使用特定依赖的代码位置，并使用 AI 进行迁移
 */
class DependencyCodeMigrator {
  constructor(projectPath, dependencyMapper, options = {}) {
    this.projectPath = projectPath;
    this.dependencyMapper = dependencyMapper;
    this.options = {
      aiApiKey: options.aiApiKey,
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      ...options
    };
    
    // 直接使用 AI 服务
    this.aiService = new AIService({
      apiKey: this.options.aiApiKey
    });
  }

  /**
   * 迁移所有依赖相关的代码
   */
  async migrateAllDependencyCode() {
    const migrationDeps = this.dependencyMapper.getMigrationDependencies(
      path.join(this.projectPath, 'package.json')
    );

    if (migrationDeps.length === 0) {
      console.log('📦 没有需要迁移的依赖代码');
      return { totalFiles: 0, migratedFiles: 0, errors: [] };
    }

    console.log(chalk.blue(`🔄 开始迁移 ${migrationDeps.length} 个依赖的代码...`));

    let totalFiles = 0;
    let migratedFiles = 0;
    const errors = [];

    for (const dep of migrationDeps) {
      try {
        console.log(chalk.gray(`\n📦 处理依赖: ${dep.source} → ${dep.target}`));
        
        const result = await this.migrateDependencyCode(dep);
        totalFiles += result.totalFiles;
        migratedFiles += result.migratedFiles;
        errors.push(...result.errors);

      } catch (error) {
        console.error(chalk.red(`❌ 迁移依赖 ${dep.source} 失败:`), error.message);
        errors.push({
          dependency: dep.source,
          error: error.message
        });
      }
    }

    return { totalFiles, migratedFiles, errors };
  }

  /**
   * 迁移单个依赖的代码
   */
  async migrateDependencyCode(dependency) {
    // 1. 找到使用该依赖的文件
    const usageFiles = await this.findDependencyUsage(dependency.source);
    
    if (usageFiles.length === 0) {
      console.log(chalk.gray(`   未找到 ${dependency.source} 的使用`));
      return { totalFiles: 0, migratedFiles: 0, errors: [] };
    }

    console.log(chalk.gray(`   找到 ${usageFiles.length} 个使用 ${dependency.source} 的文件`));

    // 2. 获取迁移文档
    const migrationDoc = this.dependencyMapper.getMigrationDoc(dependency.source);
    
    // 3. 为每个文件生成 AI 迁移提示
    let migratedFiles = 0;
    const errors = [];

    for (const filePath of usageFiles) {
      try {
        const success = await this.migrateFileWithAI(filePath, dependency, migrationDoc);
        if (success) {
          migratedFiles++;
        }
      } catch (error) {
        console.error(chalk.red(`   ❌ 迁移文件失败: ${filePath}`), error.message);
        errors.push({
          file: filePath,
          dependency: dependency.source,
          error: error.message
        });
      }
    }

    return {
      totalFiles: usageFiles.length,
      migratedFiles,
      errors
    };
  }

  /**
   * 找到使用特定依赖的文件
   */
  async findDependencyUsage(dependencyName) {
    const patterns = [
      `${this.projectPath}/src/**/*.{vue,js,ts}`,
      `${this.projectPath}/src/**/*.jsx`,
      `${this.projectPath}/src/**/*.tsx`
    ];

    const files = [];
    
    for (const pattern of patterns) {
      const matchedFiles = glob.sync(pattern);
      files.push(...matchedFiles);
    }

    // 去重
    const uniqueFiles = [...new Set(files)];
    
    // 检查每个文件是否使用了该依赖
    const usageFiles = [];
    
    for (const filePath of uniqueFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查是否导入了该依赖
        const importPatterns = [
          new RegExp(`import.*from\\s+['"\`]${dependencyName}['"\`]`, 'g'),
          new RegExp(`import\\s+['"\`]${dependencyName}['"\`]`, 'g'),
          new RegExp(`require\\s*\\(\\s*['"\`]${dependencyName}['"\`]\\s*\\)`, 'g'),
          new RegExp(`from\\s+['"\`]${dependencyName}['"\`]`, 'g')
        ];

        const hasUsage = importPatterns.some(pattern => pattern.test(content));
        
        if (hasUsage) {
          usageFiles.push(filePath);
        }
      } catch (error) {
        if (this.options.verbose) {
          console.warn(chalk.yellow(`⚠️  读取文件失败: ${filePath}`), error.message);
        }
      }
    }

    return usageFiles;
  }

  /**
   * 使用 AI 迁移单个文件
   */
  async migrateFileWithAI(filePath, dependency, migrationDoc) {
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      
      // 构建 AI 提示
      const prompt = this.buildMigrationPrompt(dependency, migrationDoc, originalContent);
      
      console.log(chalk.gray(`   🤖 AI 迁移: ${path.relative(this.projectPath, filePath)}`));
      
      if (!this.aiService.enabled) {
        console.log(chalk.yellow(`   ⚠️  AI 服务未启用，跳过迁移`));
        return false;
      }

      if (this.options.dryRun) {
        console.log(chalk.gray(`   📝 预览模式，跳过实际修改`));
        return true;
      }
      
      // 使用 AI 进行迁移
      const repairedContent = await this.aiService.callAI(prompt);
      
      if (repairedContent && repairedContent.trim()) {
        // 备份原文件
        const backupPath = filePath + '.backup';
        if (!fs.existsSync(backupPath)) {
          fs.copySync(filePath, backupPath);
        }

        // 写入修复后的内容
        fs.writeFileSync(filePath, repairedContent, 'utf8');

        console.log(chalk.green(`   ✅ 迁移成功: ${path.relative(this.projectPath, filePath)}`));
        return true;
      } else {
        console.log(chalk.yellow(`   ⚠️  AI 返回空内容: ${path.relative(this.projectPath, filePath)}`));
        return false;
      }
    } catch (error) {
      console.error(chalk.red(`   ❌ AI 迁移失败: ${filePath}`), error.message);
      return false;
    }
  }

  /**
   * 构建 AI 迁移提示
   */
  buildMigrationPrompt(dependency, migrationDoc, fileContent) {
    let prompt = `请将以下代码中的 ${dependency.source} 迁移到 ${dependency.target}。\n\n`;
    
    if (migrationDoc) {
      // 提取迁移文档中的关键信息
      const docLines = migrationDoc.split('\n');
      const usageSection = this.extractUsageSection(docLines);
      
      if (usageSection) {
        prompt += `迁移指南:\n${usageSection}\n\n`;
      }
    }

    prompt += `主要变更:\n`;
    prompt += `1. 将 import 语句从 '${dependency.source}' 改为 '${dependency.target}'\n`;
    prompt += `2. 根据新版本的 API 调整使用方式\n`;
    prompt += `3. 保持代码的功能不变\n\n`;
    
    prompt += `原始代码:\n\`\`\`\n${fileContent}\n\`\`\`\n\n`;
    prompt += `请提供迁移后的完整代码，只返回代码内容，不要包含解释。`;

    return prompt;
  }

  /**
   * 从迁移文档中提取使用示例部分
   */
  extractUsageSection(docLines) {
    let inUsageSection = false;
    let usageLines = [];
    
    for (const line of docLines) {
      if (line.includes('## 使用方法') || line.includes('## Usage')) {
        inUsageSection = true;
        continue;
      }
      
      if (inUsageSection) {
        if (line.startsWith('## ') && !line.includes('使用') && !line.includes('Usage')) {
          break;
        }
        usageLines.push(line);
      }
    }
    
    return usageLines.length > 0 ? usageLines.join('\n').trim() : null;
  }

  /**
   * 生成迁移报告
   */
  generateMigrationReport(result) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalFiles: result.totalFiles,
        migratedFiles: result.migratedFiles,
        successRate: result.totalFiles > 0 ? (result.migratedFiles / result.totalFiles * 100).toFixed(1) : 0
      },
      errors: result.errors
    };

    console.log(chalk.blue('\n📊 依赖代码迁移报告:'));
    console.log(`总文件数: ${report.summary.totalFiles}`);
    console.log(`成功迁移: ${report.summary.migratedFiles}`);
    console.log(`成功率: ${report.summary.successRate}%`);
    
    if (report.errors.length > 0) {
      console.log(chalk.yellow(`错误数: ${report.errors.length}`));
    }

    return report;
  }
}

module.exports = DependencyCodeMigrator;
