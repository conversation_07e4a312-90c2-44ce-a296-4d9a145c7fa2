---
source: v-charts
target: vue-echarts
link: https://github.com/ecomfe/vue-echarts
---

# 从 v-charts 迁移到 vue-echarts

`v-charts` 是一个基于 Vue 2 和 ECharts 封装的图表组件，它通过简化配置和统一数据格式，让图表生成变得非常简单。然而，随着 Vue 3 的普及和 ECharts 本身的不断强大，许多项目选择迁移到更灵活、更接近 ECharts 原生体验的 `vue-echarts`。

`vue-echarts` 是一个为 Vue 3 和 Vue 2 设计的 ECharts 封装组件，它让你能够以更"原生"的方式使用 ECharts 的完整功能。本次迁移的核心是从 `v-charts` 的高度抽象配置转向 `vue-echarts` 的 ECharts 标准 `option` 配置。

## 主要区别

- **抽象层次**: `v-charts` 对 ECharts 的 `option` 进行了高度封装，你通过 `columns` 和 `rows` 来定义数据。而 `vue-echarts` 则直接使用 ECharts 的 `option` 对象，让你完全掌控图表的每一个细节。
- **灵活性**: `v-charts` 简单易用，但定制化能力有限。`vue-echarts` 提供了最大的灵活性，你可以使用 ECharts 官方文档中的任何配置项。
- **组件使用**: `v-charts` 为每种图表（如 `<ve-line>`, `<ve-bar>`）提供一个组件。`vue-echarts` 通常使用一个统一的 `<v-chart>` 组件。

## 安装

首先，你需要安装 `vue-echarts` 和它的核心依赖 `echarts`。

```bash
# 卸载 v-charts
npm uninstall v-charts

# 安装 vue-echarts 和 echarts
npm install echarts vue-echarts
```

## 迁移核心：数据格式转换

迁移最关键的一步是将 `v-charts` 的 `chartData`（包含 `columns` 和 `rows`）转换为 `vue-echarts` 所需的 `option` 对象。

### 示例：从线图迁移

假设你有一个使用 `v-charts` 的线图。

**`v-charts` 用法:**
```vue
<template>
  <ve-line :data="chartData" />
</template>

<script>
export default {
  data() {
    return {
      chartData: {
        columns: ['日期', '销售额-A', '销售额-B'],
        rows: [
          { '日期': '周一', '销售额-A': 120, '销售额-B': 80 },
          { '日期': '周二', '销售额-A': 200, '销售额-B': 130 },
          { '日期': '周三', '销售额-A': 150, '销售额-B': 110 }
        ]
      }
    }
  }
}
</script>
```

**迁移到 `vue-echarts`:**

你需要将上述 `chartData` 转换为 ECharts 的 `option` 格式。

```vue
<template>
  <v-chart class="chart" :option="option" />
</template>

<script setup>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import VChart, { THEME_KEY } from 'vue-echarts';
import { ref, provide } from 'vue';

// 按需引入 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

// 如果需要，可以提供一个主题
provide(THEME_KEY, 'dark');

// 源数据
const chartData = {
  columns: ['日期', '销售额-A', '销售额-B'],
  rows: [
    { '日期': '周一', '销售额-A': 120, '销售额-B': 80 },
    { '日期': '周二', '销售额-A': 200, '销售额-B': 130 },
    { '日期': '周三', '销售额-A': 150, '销售额-B': 110 }
  ]
};

// 将 v-charts 数据转换为 ECharts option
const dimension = chartData.columns[0];
const metrics = chartData.columns.slice(1);

const option = ref({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: metrics
  },
  xAxis: {
    type: 'category',
    data: chartData.rows.map(row => row[dimension])
  },
  yAxis: {
    type: 'value'
  },
  series: metrics.map(metric => ({
    name: metric,
    type: 'line',
    data: chartData.rows.map(row => row[metric])
  }))
});
</script>

<style scoped>
.chart {
  height: 400px;
}
</style>
```

**转换逻辑解析**:
1.  **X轴 (xAxis)**: 通常是 `columns` 数组的第一个元素。其数据 `data` 来自于 `rows` 数组中每个对象的相应属性值。
2.  **Y轴 (yAxis)**: 通常是数值轴，`type: 'value'`。
3.  **系列 (series)**: `columns` 数组中除第一个元素外的其余所有元素都代表一个系列。你需要遍历这些指标，为每个指标创建一个系列对象，并从 `rows` 中提取对应的数据。

## 总结

从 `v-charts` 迁移到 `vue-echarts` 是一次从"便捷"到"专业"的转变。虽然你需要编写更多的代码来构建 `option` 对象，但作为回报，你获得了 ECharts 的全部能力和无与伦比的社区支持。

这个过程的关键在于理解 `v-charts` 的数据结构如何映射到 ECharts 的 `series`, `xAxis`, `yAxis` 等核心概念。一旦掌握了这种转换，你就可以轻松地将任何 `v-charts` 图表迁移过来，并进行更深入的定制。

建议常备 [ECharts 官方配置项手册](https://echarts.apache.org/zh/option.html) 在手边，以便查阅所有可用的图表选项。 