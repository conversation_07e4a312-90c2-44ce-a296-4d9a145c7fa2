const { execSync, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const { AIService } = require('../ai/ai-service');
const ConfigLoader = require('./configLoader');

/**
 * 构建错误修复器
 * 继承自 AIService，具备 AI 修复能力
 */

class BuildFixer extends AIService {
  constructor(projectPath, options = {}) {
    super(options);

    this.projectPath = projectPath;
    this.buildErrors = [];
    this.fixedErrors = [];
    this.failedErrors = [];

    // 配置加载器
    this.configLoader = new ConfigLoader();

    // 临时存储传入的选项，将在 initialize 中与配置文件合并
    this.userOptions = options;
    this.options = null; // 将在 initialize 中设置

    this.configPath = path.join(__dirname, '../../config/package-recommend.json');
    this.config = null;

    // 进度指示器
    this.spinner = null;

    // 构建统计信息
    this.buildStats = {
      buildAttempts: 0,
      buildSuccess: false,
      errorsFound: [],
      errorsFixed: 0,
      finalBuildSuccess: false,
      startTime: null,
      endTime: null,
      duration: 0
    };
  }

  /**
   * 启动进度指示器
   */
  startSpinner(text) {
    if (this.options && this.options.showProgress && !this.options.verbose) {
      this.spinner = ora(text).start();
    } else {
      console.log(chalk.blue(`🔄 ${text}`));
    }
  }

  /**
   * 更新进度指示器文本
   */
  updateSpinner(text) {
    if (this.spinner) {
      this.spinner.text = text;
    } else if (!this.options || !this.options.verbose) {
      console.log(chalk.blue(`🔄 ${text}`));
    }
  }

  /**
   * 成功完成进度指示器
   */
  succeedSpinner(text) {
    if (this.spinner) {
      this.spinner.succeed(text);
      this.spinner = null;
    } else {
      console.log(chalk.green(`✅ ${text}`));
    }
  }

  /**
   * 失败完成进度指示器
   */
  failSpinner(text) {
    if (this.spinner) {
      this.spinner.fail(text);
      this.spinner = null;
    } else {
      console.log(chalk.red(`❌ ${text}`));
    }
  }

  /**
   * 停止进度指示器
   */
  stopSpinner() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 加载配置文件
   */
  async loadConfig() {
    try {
      this.config = await fs.readJson(this.configPath);
    } catch (error) {
      console.error(chalk.red('❌ 无法加载配置文件:'), error.message);
      throw new Error(`Failed to load config file: ${this.configPath}`);
    }
  }

  /**
   * 获取模块映射表
   */
  getModuleMapping() {
    const mapping = {};

    // 从已知不兼容包中获取替代方案
    Object.entries(this.config.knownIncompatible).forEach(([name, info]) => {
      if (info.alternatives && info.alternatives.length > 0) {
        // 使用第一个替代方案作为主要映射
        mapping[name] = info.alternatives[0];
      } else {
        // 如果没有替代方案，标记为需要删除
        mapping[name] = null;
      }
    });

    return mapping;
  }

  /**
   * 执行构建并修复错误 - 主入口方法
   */
  async buildAndFix() {
    this.buildStats.startTime = Date.now();

    try {
      this.startSpinner('开始构建项目并修复错误...');

      // 初始化
      await this.initialize();

      // 首次构建尝试
      this.updateSpinner('执行首次构建...');
      const buildResult = await this.performBuild();

      if (buildResult.success) {
        this.succeedSpinner('项目构建成功！');
        this.buildStats.buildSuccess = true;
        this.buildStats.finalBuildSuccess = true;
        return this.createResult(true);
      }

      // 分析和修复错误
      this.updateSpinner('分析构建错误...');
      const fixResult = await this.analyzeAndFixErrors(buildResult);

      this.stopSpinner();
      this.printBuildStats();
      return fixResult;
    } catch (error) {
      this.failSpinner('构建修复过程失败');
      console.error(chalk.red('❌ 错误详情:'), error.message);
      throw error;
    } finally {
      this.buildStats.endTime = Date.now();
      this.buildStats.duration = this.buildStats.endTime - this.buildStats.startTime;
      this.stopSpinner();
    }
  }

  /**
   * 初始化构建修复器
   */
  async initialize() {
    // 加载配置文件并与用户选项合并
    const configFileOptions = await this.configLoader.loadConfig(
      this.userOptions.configPath,
      this.projectPath
    );

    // 合并配置：用户选项 > 配置文件 > 默认值
    this.options = {
      ...configFileOptions,
      ...this.userOptions
    };

    // 加载包推荐配置
    await this.loadConfig();

    if (this.options.verbose) {
      console.log(chalk.gray(`📁 项目路径: ${this.projectPath}`));
      console.log(chalk.gray(`🔧 构建命令: ${this.options.buildCommand}`));
      console.log(chalk.gray(`🔄 最大尝试次数: ${this.options.maxAttempts}`));
      console.log(chalk.gray(`🤖 AI 修复: ${this.options.skipAI ? '禁用' : '启用'}`));
      console.log(chalk.gray(`🔍 预览模式: ${this.options.dryRun ? '是' : '否'}`));
    }
  }

  /**
   * 分析和修复错误
   */
  async analyzeAndFixErrors(buildResult) {
    let errors = this.parseErrors(buildResult.output);
    this.buildStats.errorsFound = errors;

    if (errors.length === 0) {
      console.log(chalk.yellow('⚠️  构建失败但无法解析错误信息'));
      return this.createResult(false, 'Unable to parse build errors');
    }

    console.log(chalk.yellow(`发现 ${errors.length} 个构建错误，开始修复...`));

    // 尝试修复错误
    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxAttempts}...`));

      const fixResult = await this.fixErrors(errors);

      if (fixResult.fixed > 0) {
        console.log(chalk.green(`✅ 修复了 ${fixResult.fixed} 个错误`));
        this.buildStats.errorsFixed += fixResult.fixed;

        // 重新构建
        const newBuildResult = await this.performBuild();

        if (newBuildResult.success) {
          console.log(chalk.green('🎉 修复后构建成功！'));
          this.buildStats.finalBuildSuccess = true;
          break;
        } else {
          // 更新错误列表
          errors = this.parseErrors(newBuildResult.output);
          console.log(chalk.yellow(`仍有 ${errors.length} 个错误需要修复`));
        }
      } else {
        console.log(chalk.yellow('⚠️  本轮未能修复任何错误'));
      }
    }

    return this.createResult(this.buildStats.finalBuildSuccess, null, errors.length);
  }

  /**
   * 创建结果对象
   */
  createResult(success, reason = null, remainingErrors = 0) {
    return {
      success,
      attempts: this.buildStats.buildAttempts,
      errorsFixed: this.buildStats.errorsFixed,
      remainingErrors,
      duration: this.buildStats.duration,
      reason
    };
  }

  /**
   * 执行构建过程（包含依赖安装和构建）
   */
  async performBuild() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行构建
      return await this.executeBuild();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 安装项目依赖
   */
  async installDependencies() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过依赖安装'));
      return;
    }

    this.updateSpinner('正在安装依赖...');

    try {
      const installOutput = execSync(this.options.installCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 依赖安装完成'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(installOutput));
        this.startSpinner('继续处理...');
      }
    } catch (error) {
      // 尝试使用 legacy peer deps 重新安装
      if (this.options.useLegacyPeerDeps && this.shouldUseLegacyPeerDeps(error)) {
        await this.installWithLegacyPeerDeps();
      } else {
        this.stopSpinner();
        console.log(chalk.yellow('⚠️ 依赖安装可能不完整，继续尝试构建'));
        this.logInstallError(error);
        this.startSpinner('继续处理...');
      }
    }
  }

  /**
   * 检查是否应该使用 legacy peer deps
   */
  shouldUseLegacyPeerDeps(error) {
    const errorOutput = error.stdout + error.stderr;
    return errorOutput.includes('ERESOLVE unable to resolve dependency tree') ||
           errorOutput.includes('Fix the upstream dependency conflict') ||
           errorOutput.includes('--legacy-peer-deps') ||
           errorOutput.includes('peer dependency') ||
           errorOutput.includes('Could not resolve dependency');
  }

  /**
   * 使用 legacy peer deps 安装依赖
   */
  async installWithLegacyPeerDeps() {
    this.updateSpinner('检测到依赖冲突，尝试使用 --legacy-peer-deps 重新安装...');

    try {
      const legacyCommand = this.options.installCommand + ' --legacy-peer-deps';
      const legacyInstallOutput = execSync(legacyCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 使用 --legacy-peer-deps 安装依赖成功'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(legacyInstallOutput));
        this.startSpinner('继续处理...');
      }
    } catch (legacyError) {
      this.stopSpinner();
      console.log(chalk.red('❌ 即使使用 --legacy-peer-deps 也无法安装依赖'));
      this.logInstallError(legacyError);
      this.startSpinner('继续处理...');
    }
  }

  /**
   * 执行构建命令
   */
  async executeBuild() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过构建执行'));
      return { success: true, output: 'DRY RUN - Build skipped' };
    }

    this.updateSpinner(`执行构建命令: ${this.options.buildCommand}`);
    this.buildStats.buildAttempts++;

    try {
      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return {
        success: true,
        output
      };
    } catch (error) {
      this.stopSpinner();
      console.log(chalk.red('❌ 构建失败'));
      const result = this.handleBuildError(error);
      this.startSpinner('继续处理...');
      return result;
    }
  }

  /**
   * 处理构建错误
   */
  handleBuildError(error) {
    const errorOutput = error.stdout + error.stderr;
    const errorSummary = this.extractErrorSummary(errorOutput);

    if (errorSummary.length > 0) {
      console.log(chalk.red('\n构建错误摘要:'));
      errorSummary.slice(0, 10).forEach(line => {
        console.log(chalk.gray(`  ${line.trim()}`));
      });

      if (errorSummary.length > 10) {
        console.log(chalk.gray(`  ... 以及 ${errorSummary.length - 10} 个其他错误`));
      }
    } else {
      console.log(chalk.yellow('⚠️ 无法提取具体错误信息'));
      this.logBasicErrorInfo(errorOutput);
      this.suggestCommonSolutions(errorOutput);
    }

    return {
      success: false,
      output: errorOutput,
      errorSummary: errorSummary.join('\n'),
      error: error.message
    };
  }

  /**
   * 解析构建错误
   */
  parseErrors(buildOutput) {
    const errors = [];

    // 确保 buildOutput 是字符串
    const outputStr = typeof buildOutput === 'string' ? buildOutput : String(buildOutput || '');
    const lines = outputStr.split('\n');

    let currentError = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检测错误开始
      const errorMatch = this.detectErrorStart(line);
      if (errorMatch) {
        if (currentError) {
          errors.push(currentError);
        }
        currentError = {
          type: errorMatch.type,
          file: errorMatch.file,
          line: errorMatch.line,
          column: errorMatch.column,
          message: errorMatch.message,
          fullMessage: [line],
          severity: errorMatch.severity || 'error'
        };
      } else if (currentError && this.isErrorContinuation(line)) {
        currentError.fullMessage.push(line);
        if (line.trim()) {
          currentError.message += ' ' + line.trim();
        }
      } else if (currentError) {
        errors.push(currentError);
        currentError = null;
      }
    }

    if (currentError) {
      errors.push(currentError);
    }

    return this.categorizeErrors(errors);
  }

  /**
   * 检测错误开始
   */
  detectErrorStart(line) {
    // TypeScript 错误 - 更宽松的匹配
    const tsError = line.match(/(.+\.(?:ts|vue))\((\d+),(\d+)\):\s*(error|warning)\s*(?:TS(\d+):)?\s*(.+)/);
    if (tsError) {
      return {
        type: 'typescript',
        file: tsError[1],
        line: parseInt(tsError[2]),
        column: parseInt(tsError[3]),
        severity: tsError[4],
        code: tsError[5] || 'unknown',
        message: tsError[6]
      };
    }

    // Vue 编译错误
    const vueError = line.match(/(.+\.vue):(\d+):(\d+):\s*(.*)/);
    if (vueError) {
      return {
        type: 'vue',
        file: vueError[1],
        line: parseInt(vueError[2]),
        column: parseInt(vueError[3]),
        message: vueError[4]
      };
    }

    // Webpack/Vite 错误
    const webpackError = line.match(/ERROR in (.+)/);
    if (webpackError) {
      return {
        type: 'webpack',
        file: webpackError[1],
        message: line
      };
    }

    // ESLint 错误
    const eslintError = line.match(/(.+\.(?:js|vue|ts)):(\d+):(\d+):\s*(error|warning)\s*(.+)/);
    if (eslintError) {
      return {
        type: 'eslint',
        file: eslintError[1],
        line: parseInt(eslintError[2]),
        column: parseInt(eslintError[3]),
        severity: eslintError[4],
        message: eslintError[5]
      };
    }

    // 通用错误
    const genericError = line.match(/Error:\s*(.+)/);
    if (genericError) {
      return {
        type: 'generic',
        message: genericError[1]
      };
    }

    return null;
  }

  /**
   * 检查是否为错误信息的继续
   */
  isErrorContinuation(line) {
    return line.startsWith('    ') || line.startsWith('\t') ||
           line.includes('at ') || line.includes('in ');
  }

  /**
   * 分类错误
   */
  categorizeErrors(errors) {
    return errors.map(error => {
      // 根据错误信息分类
      if (error.message.includes('Cannot find module')) {
        error.category = 'missing-module';
      } else if (error.message.includes('Property') && error.message.includes('does not exist')) {
        error.category = 'property-not-exist';
      } else if (error.message.includes('is not a function')) {
        error.category = 'not-a-function';
      } else if (error.message.includes('Vue 2') || error.message.includes('Vue3')) {
        error.category = 'vue-version';
      } else if (error.message.includes('element-ui') || error.message.includes('element-plus')) {
        error.category = 'ui-library';
      } else {
        error.category = 'other';
      }

      return error;
    });
  }

  /**
   * 修复错误
   */
  async fixErrors(errors) {
    let fixedCount = 0;

    for (const error of errors) {
      try {
        const fixed = await this.fixSingleError(error);
        if (fixed) {
          fixedCount++;
        }
      } catch (fixError) {
        console.log(chalk.yellow(`⚠️  修复错误失败: ${error.file} - ${fixError.message}`));
      }
    }

    return { fixed: fixedCount };
  }

  /**
   * 修复单个错误
   */
  async fixSingleError(error) {
    console.log(chalk.gray(`🔧 修复: ${error.file || 'unknown'} - ${error.category}`));

    switch (error.category) {
      case 'missing-module':
        return await this.fixMissingModule(error);
      case 'property-not-exist':
        return await this.fixPropertyNotExist(error);
      case 'vue-version':
        return await this.fixVueVersionIssue(error);
      case 'ui-library':
        return await this.fixUILibraryIssue(error);
      default:
        return await this.fixWithAI(error);
    }
  }

  /**
   * 修复缺失模块错误
   */
  async fixMissingModule(error) {
    const moduleMatch = error.message.match(/Cannot find module ['"]([^'"]+)['"]/);
    if (!moduleMatch) return false;

    const moduleName = moduleMatch[1];

    // 获取模块映射表
    const moduleMapping = this.getModuleMapping();

    if (Object.prototype.hasOwnProperty.call(moduleMapping, moduleName)) {
      const replacement = moduleMapping[moduleName];
      if (replacement) {
        return await this.replaceImport(error.file, moduleName, replacement);
      } else {
        return await this.removeImport(error.file, moduleName);
      }
    }

    return false;
  }

  /**
   * 修复属性不存在错误
   */
  async fixPropertyNotExist(error) {
    // 使用 AI 修复复杂的属性错误
    return await this.fixWithAI(error);
  }

  /**
   * 修复 Vue 版本问题
   */
  async fixVueVersionIssue(error) {
    return await this.fixWithAI(error);
  }

  /**
   * 修复 UI 库问题
   */
  async fixUILibraryIssue(error) {
    return await this.fixWithAI(error);
  }

  /**
   * 使用 AI 修复错误
   */
  async fixWithAI(error) {
    // 优先使用内置 AI 服务，如果不可用则使用外部 aiRepairer
    if (this.isEnabled()) {
      try {
        // 如果是 AI 分析类型的错误，已经分析过了，直接返回成功
        if (error.category === 'ai-analyzed' && error.aiAnalysis) {
          console.log(chalk.green('✅ 错误已由 AI 分析处理'));
          return true;
        }

        const result = await this.repairBuildError(error);
        return result.success;
      } catch (aiError) {
        console.log(chalk.yellow(`⚠️  内置 AI 修复失败: ${aiError.message}`));
      }
    }

    // 回退到外部 aiRepairer
    if (this.options.aiRepairer && this.options.aiRepairer.isEnabled()) {
      try {
        const result = await this.options.aiRepairer.repairSingleFile({
          file: error.file,
          absolutePath: path.join(this.projectPath, error.file),
          error: error.message
        }, this.projectPath);

        return result.success;
      } catch (aiError) {
        console.log(chalk.yellow(`⚠️  外部 AI 修复失败: ${aiError.message}`));
      }
    }

    return false;
  }

  /**
   * 使用 AI 修复构建错误
   */
  async repairBuildError(error) {
    const filePath = path.join(this.projectPath, error.file);

    try {
      // 读取原始文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');

      // 生成构建错误专用提示
      const prompt = this.generateBuildErrorPrompt(originalContent, error);

      // 调用 AI 进行修复
      const repairedContent = await this.callAI(prompt);

      // 验证修复结果
      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // 备份原文件
        await this.backupFile(filePath);

        // 写入修复后的内容
        await fs.writeFile(filePath, repairedContent, 'utf8');

        console.log(chalk.green(`✅ 构建错误修复成功: ${error.file}`));
        return {
          file: error.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  构建错误修复结果验证失败: ${error.file}`));
        return {
          file: error.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (repairError) {
      console.log(chalk.red(`❌ 构建错误修复失败: ${error.file}`));
      return {
        file: error.file,
        success: false,
        error: repairError.message
      };
    }
  }

  /**
   * 生成构建错误专用提示词
   */
  generateBuildErrorPrompt(originalContent, error) {
    const fileExtension = path.extname(error.file);
    const errorMessage = error.message;
    const errorType = error.type;
    const errorCategory = error.category;

    let prompt = `你是一个专业的 Vue 2 到 Vue 3 迁移专家，现在需要修复构建过程中出现的错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**错误信息**：
- 错误类型：${errorType}
- 错误分类：${errorCategory}
- 错误消息：${errorMessage}
- 文件路径：${error.file}
${error.line ? `- 错误行号：${error.line}` : ''}
${error.column ? `- 错误列号：${error.column}` : ''}

**修复要求**：
1. **保持功能一致性**：修复后的代码必须保持原有功能不变
2. **Vue 3 兼容性**：确保代码符合 Vue 3 语法和最佳实践
3. **Element Plus 兼容性**：将 Element UI 组件正确替换为 Element Plus
4. **TypeScript 兼容性**：如果是 TypeScript 文件，确保类型定义正确
5. **构建兼容性**：修复后的代码必须能够通过构建流程

**常见构建错误修复指南**：`;

    // 根据错误类型添加特定指导
    switch (errorCategory) {
      case 'missing-module':
        prompt += `

**缺失模块错误修复**：
- 检查模块导入路径是否正确
- 将 Vue 2 相关模块替换为 Vue 3 对应模块
- 将 Element UI 导入替换为 Element Plus
- 移除 Vue 3 中已内置的模块（如 @vue/composition-api）`;
        break;

      case 'property-not-exist':
        prompt += `

**属性不存在错误修复**：
- 检查 Vue 3 中是否移除了该属性
- 使用 Vue 3 的新 API 替换已废弃的属性
- 更新组件实例的属性访问方式
- 修复 this.$refs 的访问方式`;
        break;

      case 'vue-version':
        prompt += `

**Vue 版本兼容性错误修复**：
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新生命周期钩子名称（如 beforeDestroy → beforeUnmount）
- 修复事件处理器的语法变化`;
        break;

      case 'ui-library':
        prompt += `

**UI 库兼容性错误修复**：
- 将 Element UI 组件名替换为 Element Plus 对应组件
- 更新组件属性名称和事件名称
- 修复图标引用方式
- 更新主题和样式引用`;
        break;

      default:
        prompt += `

**通用构建错误修复**：
- 检查语法错误和拼写错误
- 确保导入导出语句正确
- 修复类型定义问题
- 解决依赖冲突`;
    }

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定修复要求**：
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 在 <script> 中使用 defineComponent 或 Composition API
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义
- 修复 v-model 的使用方式`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript/TypeScript 文件特定修复要求**：
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句和模块路径
- 更新类型定义（TypeScript）`;
    }

    prompt += `

**原始代码**：
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

**输出要求**：
1. 请仔细分析错误原因，提供修复后的完整代码
2. 使用 ${fileExtension.slice(1)} 代码块包装修复后的代码
3. 确保修复后的代码能够解决构建错误
4. 保持代码的可读性和维护性
5. 如果需要添加新的导入，请确保模块路径正确

请提供修复后的完整代码：`;

    return prompt;
  }

  /**
   * 替换导入语句
   */
  async replaceImport(filePath, oldModule, newModule) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;

      const content = await fs.readFile(fullPath, 'utf8');
      const newContent = content.replace(
        new RegExp(`(['"])${oldModule}\\1`, 'g'),
        `$1${newModule}$1`
      );

      if (newContent !== content) {
        await fs.writeFile(fullPath, newContent, 'utf8');
        console.log(chalk.green(`✅ 替换导入: ${oldModule} → ${newModule}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 替换导入失败: ${error.message}`));
    }

    return false;
  }

  /**
   * 移除导入语句
   */
  async removeImport(filePath, moduleName) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;

      const content = await fs.readFile(fullPath, 'utf8');
      const lines = content.split('\n');
      const newLines = lines.filter(line =>
        !line.includes(`'${moduleName}'`) && !line.includes(`"${moduleName}"`)
      );

      if (newLines.length !== lines.length) {
        await fs.writeFile(fullPath, newLines.join('\n'), 'utf8');
        console.log(chalk.green(`✅ 移除导入: ${moduleName}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 移除导入失败: ${error.message}`));
    }

    return false;
  }

  /**
   * 打印构建统计
   */
  printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);
  }

  /**
   * 验证修复后的内容（构建错误专用验证）
   */
  validateRepairedContent(repairedContent, originalContent) {
    // 调用基类的验证方法
    if (!super.validateRepairedContent(repairedContent, originalContent)) {
      return false;
    }

    // 构建错误特定的验证
    const buildErrorMarkers = ['COMPILATION_ERROR', 'BUILD_ERROR', 'SYNTAX_ERROR'];
    if (buildErrorMarkers.some(marker => repairedContent.includes(marker))) {
      return false;
    }

    // 检查是否包含基本的 Vue 组件结构
    if (originalContent.includes('<template>') && !repairedContent.includes('<template>')) {
      return false;
    }

    return true;
  }

  /**
   * 获取构建统计信息
   */
  getBuildStats() {
    return { ...this.buildStats };
  }

  /**
   * 提取错误摘要
   */
  extractErrorSummary(errorOutput) {
    return errorOutput.split('\n').filter(line =>
      line.includes('ERROR') ||
      line.includes('Error:') ||
      line.includes('error') ||
      line.includes('Failed')
    );
  }

  /**
   * 记录安装错误
   */
  logInstallError(error) {
    console.error(chalk.red('安装错误:'), error.message);

    if (this.options.verbose) {
      console.log(chalk.gray('详细错误信息:'));
      console.log(chalk.red(error.stdout + error.stderr));
    }
  }

  /**
   * 建议常见解决方案
   */
  suggestCommonSolutions(errorOutput) {
    if (errorOutput.includes('ETARGET') || errorOutput.includes('No matching version found')) {
      console.log(chalk.yellow('\n⚠️ 依赖包版本问题:'));
      console.log(chalk.gray('  可能存在一个或多个依赖包找不到匹配的版本'));
      console.log(chalk.gray('  建议: 检查 package.json 中指定的版本是否存在，或尝试使用兼容 Vue 3 的替代库'));
    }

    if (errorOutput.includes('peer dependency') || errorOutput.includes('ERESOLVE')) {
      console.log(chalk.yellow('\n⚠️ 依赖冲突问题:'));
      console.log(chalk.gray('  检测到 peer dependency 冲突，某些库可能需要特定版本的 Vue'));
      console.log(chalk.gray('  建议: 更新到兼容 Vue 3 的库版本，或使用 --force 强制安装'));
    }
  }

  /**
   * 输出基本错误信息
   * @private
   */
  logBasicErrorInfo(errorOutput) {
    // 如果启用了详细模式，显示一部分原始错误
    if (this.options.verbose) {
      const previewLength = Math.min(1000, errorOutput.length);
      console.log(chalk.gray('错误预览:'));
      console.log(chalk.gray(errorOutput.substring(0, previewLength)));

      if (previewLength < errorOutput.length) {
        console.log(chalk.gray(`... (截断，共 ${errorOutput.length} 字符)`));
      }
    } else {
      console.log(chalk.yellow('提示: 使用 --verbose 参数可查看详细错误信息'));
    }
  }

}

module.exports = BuildFixer;
