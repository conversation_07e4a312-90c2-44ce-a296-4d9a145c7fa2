const { spawn, execSync } = require('child_process');
const chalk = require('chalk');
const path = require('path');

/**
 * 构建场景基类
 * 定义了所有构建场景的通用接口和行为
 */
class BaseScenario {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = options;
    this.errors = [];
    this.output = '';
    this.process = null;
    this.isRunning = false;
  }

  /**
   * 获取场景名称
   */
  getName() {
    throw new Error('Subclasses must implement getName()');
  }

  /**
   * 获取场景描述
   */
  getDescription() {
    throw new Error('Subclasses must implement getDescription()');
  }

  /**
   * 获取执行命令
   */
  getCommand() {
    throw new Error('Subclasses must implement getCommand()');
  }

  /**
   * 获取超时时间（毫秒）
   */
  getTimeout() {
    return this.options.timeout || 60000; // 默认 60 秒
  }

  /**
   * 执行场景
   */
  async execute() {
    throw new Error('Subclasses must implement execute()');
  }

  /**
   * 解析错误
   */
  parseErrors(output) {
    const errors = [];
    const lines = output.split('\n');
    
    for (const line of lines) {
      const error = this.parseErrorLine(line);
      if (error) {
        errors.push(error);
      }
    }
    
    return errors;
  }

  /**
   * 解析单行错误
   */
  parseErrorLine(line) {
    // TypeScript 错误
    const tsError = line.match(/(.+\.(?:ts|vue))\((\d+),(\d+)\):\s*(error|warning)\s*(?:TS(\d+):)?\s*(.+)/);
    if (tsError) {
      return {
        type: 'typescript',
        file: tsError[1],
        line: parseInt(tsError[2]),
        column: parseInt(tsError[3]),
        severity: tsError[4],
        code: tsError[5] || 'unknown',
        message: tsError[6],
        fullLine: line
      };
    }

    // Vue 编译错误
    const vueError = line.match(/(.+\.vue):(\d+):(\d+):\s*(.*)/);
    if (vueError) {
      return {
        type: 'vue',
        file: vueError[1],
        line: parseInt(vueError[2]),
        column: parseInt(vueError[3]),
        message: vueError[4],
        fullLine: line
      };
    }

    // Webpack/Vite 错误
    const webpackError = line.match(/ERROR in (.+)/);
    if (webpackError) {
      return {
        type: 'webpack',
        file: webpackError[1],
        message: line,
        fullLine: line
      };
    }

    // ESLint 错误
    const eslintError = line.match(/(.+\.(?:js|vue|ts)):(\d+):(\d+):\s*(error|warning)\s*(.+)/);
    if (eslintError) {
      return {
        type: 'eslint',
        file: eslintError[1],
        line: parseInt(eslintError[2]),
        column: parseInt(eslintError[3]),
        severity: eslintError[4],
        message: eslintError[5],
        fullLine: line
      };
    }

    // 通用错误
    if (line.includes('Error:') || line.includes('ERROR') || line.includes('error')) {
      return {
        type: 'generic',
        message: line.trim(),
        fullLine: line
      };
    }

    return null;
  }

  /**
   * 停止执行
   */
  async stop() {
    if (this.process && this.isRunning) {
      this.isRunning = false;
      
      try {
        // 尝试优雅地终止进程
        this.process.kill('SIGTERM');
        
        // 等待一段时间后强制终止
        setTimeout(() => {
          if (this.process && !this.process.killed) {
            this.process.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.warn(chalk.yellow(`⚠️  停止进程时出错: ${error.message}`));
      }
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.errors = [];
    this.output = '';
    this.process = null;
    this.isRunning = false;
  }

  /**
   * 获取结果摘要
   */
  getSummary() {
    return {
      scenario: this.getName(),
      description: this.getDescription(),
      command: this.getCommand(),
      errorsFound: this.errors.length,
      hasErrors: this.errors.length > 0,
      output: this.output
    };
  }

  /**
   * 日志输出
   */
  log(message, type = 'info') {
    const prefix = `[${this.getName()}]`;
    
    switch (type) {
      case 'error':
        console.log(chalk.red(`${prefix} ${message}`));
        break;
      case 'warning':
        console.log(chalk.yellow(`${prefix} ${message}`));
        break;
      case 'success':
        console.log(chalk.green(`${prefix} ${message}`));
        break;
      default:
        console.log(chalk.blue(`${prefix} ${message}`));
    }
  }
}

module.exports = BaseScenario;
