# 依赖配置文件说明

## 概述

`package-recommend.json` 是一个集中管理 Vue 2 到 Vue 3 迁移过程中依赖包版本信息的配置文件。通过使用这个配置文件，我们可以：

1. **统一管理版本信息**：所有依赖的版本信息都集中在一个文件中，便于维护和更新
2. **避免硬编码**：代码中不再包含硬编码的版本信息，提高代码的可维护性
3. **便于发布**：配置文件会包含在 npm 包中，确保用户使用时能获得最新的版本信息

## 文件结构

配置文件包含以下几个主要部分：

### 1. knownCompatible（已知兼容的包）

包含已经确认与 Vue 3 兼容的包及其推荐版本：

```json
{
  "knownCompatible": {
    "vue": {
      "version": "^3.4.0",
      "description": "Vue 3 core library"
    },
    "vue-router": {
      "version": "^4.5.0", 
      "description": "Vue 3 router"
    }
  }
}
```

### 2. knownIncompatible（已知不兼容的包）

包含与 Vue 3 不兼容的包及其替代方案：

```json
{
  "knownIncompatible": {
    "element-ui": {
      "alternatives": ["element-plus"],
      "description": "Vue 2 UI library, use element-plus for Vue 3"
    },
    "vue-template-compiler": {
      "alternatives": ["@vue/compiler-sfc"],
      "description": "Vue 2 template compiler, use @vue/compiler-sfc for Vue 3"
    }
  }
}
```

### 3. needsUpgrade（需要升级的包）

包含需要升级到特定版本才能与 Vue 3 兼容的包：

```json
{
  "needsUpgrade": {
    "vuedraggable": {
      "version": "^4.1.0",
      "note": "Vue 3 compatible version",
      "description": "Vue draggable component"
    }
  }
}
```

### 4. systemDependencies（系统依赖）

包含被认为是系统依赖的包列表，这些包在兼容性检查中会被自动标记为兼容：

```json
{
  "systemDependencies": [
    "fs-extra",
    "path",
    "chalk",
    "commander"
  ]
}
```

## 使用方法

### 在代码中使用

各个组件会自动加载这个配置文件：

```javascript
// DependencyChecker 会自动加载配置
const checker = new DependencyChecker(projectPath);
await checker.checkCompatibility();

// PackageUpgrader 会自动加载配置
const upgrader = new PackageUpgrader(projectPath);
await upgrader.upgrade();

// BuildFixer 会自动加载配置
const fixer = new BuildFixer(projectPath);
await fixer.buildAndFix();
```

### 手动加载配置

如果需要手动访问配置信息：

```javascript
const fs = require('fs-extra');
const path = require('path');

const configPath = path.join(__dirname, 'config/package-recommend.json');
const config = await fs.readJson(configPath);

// 访问配置信息
console.log(config.knownCompatible.vue.version);
console.log(config.knownIncompatible['element-ui'].alternatives);
```

## 维护指南

### 添加新的兼容包

1. 在 `knownCompatible` 中添加新条目
2. 提供 `version` 和 `description` 字段
3. 运行测试确保配置正确加载

### 添加新的不兼容包

1. 在 `knownIncompatible` 中添加新条目
2. 提供 `alternatives` 数组和 `description` 字段
3. 确保替代方案是有效的包名

### 更新版本信息

1. 直接修改配置文件中的版本号
2. 运行测试验证更改
3. 发布新版本时配置文件会自动包含

### 添加系统依赖

1. 在 `systemDependencies` 数组中添加包名
2. 确保这些包确实是系统级别的依赖

## 测试

运行配置文件测试：

```bash
node test/runner.js config.test.js
```

测试会验证：
- 配置文件能够正确加载
- 各个组件能够正确使用配置信息
- 依赖映射功能正常工作

## 注意事项

1. **版本格式**：使用标准的 semver 格式（如 `^3.4.0`）
2. **包名一致性**：确保包名与 npm 注册表中的名称完全一致
3. **替代方案**：为不兼容的包提供有效的替代方案
4. **描述信息**：为每个包提供清晰的描述信息，便于理解

## 发布说明

配置文件会自动包含在 npm 包中，因为 `package.json` 中的 `files` 字段包含了 `config/` 目录：

```json
{
  "files": [
    "index.js",
    "bin/",
    "src/",
    "config/"
  ]
}
```

这确保了用户安装包时能够获得最新的依赖配置信息。 