{"knownCompatible": {"vue": {"version": "^3.4.0", "description": "Vue 3 core library"}, "vue-router": {"version": "^4.5.0", "description": "Vue 3 router"}, "vuex": {"version": "^4.1.0", "description": "Vue 3 state management"}, "element-plus": {"version": "^2.9.0", "description": "Vue 3 UI component library"}, "@vue/compiler-sfc": {"version": "^3.4.0", "description": "Vue 3 SFC compiler"}, "@vue/cli-service": {"version": "^5.0.8", "description": "Vue CLI service"}, "@vue/cli-plugin-babel": {"version": "^5.0.8", "description": "Vue CLI babel plugin"}, "@vue/cli-plugin-eslint": {"version": "^5.0.8", "description": "Vue CLI eslint plugin"}, "@vue/test-utils": {"version": "^2.4.6", "description": "Vue 3 testing utilities"}, "eslint-plugin-vue": {"version": "^10.2.0", "description": "Vue ESLint plugin"}, "axios": {"description": "HTTP client library"}, "lodash": {"description": "JavaScript utility library"}, "moment": {"description": "Date manipulation library"}, "dayjs": {"description": "Modern date utility library"}, "echarts": {"description": "Charting library"}, "clipboard": {"description": "Copy to clipboard library"}, "core-js": {"description": "JavaScript polyfills"}, "js-cookie": {"description": "JavaScript cookie library"}, "normalize.css": {"description": "CSS reset library"}, "nprogress": {"description": "Progress bar library"}, "screenfull": {"description": "Fullscreen API wrapper"}, "file-saver": {"description": "File saving library"}, "xlsx": {"description": "Excel file processing library"}, "jszip": {"description": "ZIP file library"}, "fuse.js": {"description": "Fuzzy search library"}, "sortablejs": {"description": "Sortable library"}, "codemirror": {"description": "Code editor component"}, "dropzone": {"description": "File upload library"}, "driver.js": {"description": "Product tour library"}, "jsonlint": {"description": "JSON linting library"}, "path-to-regexp": {"description": "Path to regexp utility"}, "script-loader": {"description": "Script loading utility"}, "tui-editor": {"description": "Rich text editor"}}, "knownIncompatible": {"element-ui": {"alternatives": ["element-plus"], "description": "Vue 2 UI library, use element-plus for Vue 3"}, "vue-template-compiler": {"alternatives": ["@vue/compiler-sfc"], "description": "Vue 2 template compiler, use @vue/compiler-sfc for Vue 3"}, "@vue/composition-api": {"alternatives": ["Built into Vue 3"], "description": "Composition API plugin for Vue 2, built into Vue 3"}, "vue-class-component": {"alternatives": ["Use Composition API or Options API"], "description": "Class component decorator, use Composition API in Vue 3"}, "vue-property-decorator": {"alternatives": ["Use Composition API"], "description": "Property decorators for Vue 2, use Composition API in Vue 3"}, "vuex-class": {"alternatives": ["Use Composition API with useStore"], "description": "Vuex decorators for Vue 2, use Composition API in Vue 3"}}, "needsUpgrade": {"vuedraggable": {"version": "^4.1.0", "note": "Vue 3 compatible version", "description": "Vue draggable component"}, "vue-count-to": {"version": "^1.0.13", "note": "Should work with Vue 3", "description": "Vue count animation component"}, "vue-splitpane": {"version": "^1.0.6", "note": "May need manual testing", "description": "Vue split pane component"}, "sass": {"version": "^1.82.0", "note": "Latest stable version with better Vue 3 support", "description": "Modern Sass compiler"}, "sass-loader": {"version": "^13.3.0", "note": "Latest version with webpack 5 support", "description": "Sass loader for webpack"}, "typescript": {"version": "^5.3.0", "note": "Latest stable TypeScript version", "description": "TypeScript compiler"}, "webpack": {"version": "^5.89.0", "note": "Latest webpack 5 version", "description": "Module bundler"}, "eslint": {"version": "^8.56.0", "note": "Latest ESLint version", "description": "JavaScript linter"}, "postcss": {"version": "^8.4.33", "note": "Latest PostCSS version", "description": "CSS post-processor"}, "autoprefixer": {"version": "^10.4.17", "note": "Latest autoprefixer version", "description": "CSS autoprefixer"}}, "systemDependencies": ["fs-extra", "path", "util", "crypto", "os", "http", "https", "chalk", "commander", "inquirer", "ora", "semver", "plop", "webpack", "babel", "eslint", "prettier", "jest", "autoprefixer", "@vue/cli", "vue-cli-service", "husky", "lint-staged", "mockjs", "chokidar", "connect", "html-webpack-plugin", "script-ext-html-webpack-plugin", "sass", "sass-loader", "svg-sprite-loader", "svgo", "serve-static", "runjs"], "migrationSettings": {"preserveVue3Dependencies": {"description": "在迁移到 Vue 3 项目时，保留目标项目中已有的 Vue 3 兼容依赖", "enabled": true, "protectedPackages": ["vue", "vue-router", "vuex", "pinia", "element-plus", "@element-plus/icons-vue", "@vue/compiler-sfc", "@vue/cli-service", "@vue/test-utils", "eslint-plugin-vue", "@vitejs/plugin-vue", "vite", "sass", "sass-loader", "webpack", "webpack-cli", "typescript", "@types/node", "eslint", "postcss", "autoprefixer", "babel-loader", "@babel/core", "@babel/preset-env", "css-loader", "style-loader", "mini-css-extract-plugin", "html-webpack-plugin", "terser-webpack-plugin", "vue-loader", "vue-style-loader", "@vue/babel-preset-jsx", "@vue/babel-helper-vue-jsx-merge-props", "unplugin-vue-components", "unplugin-auto-import"]}, "conditionalDependencies": {"description": "只在特定条件下添加的依赖", "rules": {"element-plus": {"condition": "hasElementUI", "description": "只在项目使用 element-ui 时添加 element-plus"}, "@element-plus/icons-vue": {"condition": "hasElementUI || hasElementPlus", "description": "只在项目使用 element-ui 或 element-plus 时添加图标库"}}}}}