#!/bin/bash

# Vue Element Admin 自动迁移脚本
# 使用方法: ./migrate-vue-element-admin.sh [项目路径]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查项目
check_project() {
    local project_path=$1
    
    print_info "检查项目: $project_path"
    
    if [ ! -d "$project_path" ]; then
        print_error "项目目录不存在: $project_path"
        exit 1
    fi
    
    if [ ! -f "$project_path/package.json" ]; then
        print_error "未找到 package.json 文件"
        exit 1
    fi
    
    # 检查是否为 Vue Element Admin 项目
    if grep -q "vue-element-admin" "$project_path/package.json"; then
        print_success "检测到 Vue Element Admin 项目"
    else
        print_warning "未检测到 Vue Element Admin 项目，将使用通用迁移配置"
    fi
}

# 设置环境变量
setup_env() {
    print_info "设置环境变量..."
    
    # 检查 AI API Key
    if [ -n "$DEEPSEEK_API_KEY" ]; then
        print_success "检测到 DeepSeek API Key"
        export AI_PROVIDER="deepseek"
    elif [ -n "$GLM_API_KEY" ]; then
        print_success "检测到 GLM API Key"
        export AI_PROVIDER="glm"
    elif [ -n "$OPENAI_API_KEY" ]; then
        print_success "检测到 OpenAI API Key"
        export AI_PROVIDER="openai"
    else
        print_warning "未检测到 AI API Key，将跳过 AI 修复步骤"
        print_info "可以设置以下环境变量之一："
        print_info "  export DEEPSEEK_API_KEY=your_key"
        print_info "  export GLM_API_KEY=your_key"
        print_info "  export OPENAI_API_KEY=your_key"
    fi
}

# 执行迁移
run_migration() {
    local project_path=$1
    local dry_run=$2
    
    print_info "开始执行迁移..."
    
    # 构建命令
    local cmd="node $(dirname "$0")/../bin/vue-migrator.js auto \"$project_path\""
    
    if [ "$dry_run" = "true" ]; then
        cmd="$cmd --dry-run"
        print_info "运行预览模式（不会实际修改文件）"
    fi
    
    if [ -n "$DEEPSEEK_API_KEY" ]; then
        cmd="$cmd --ai-key $DEEPSEEK_API_KEY"
    elif [ -n "$GLM_API_KEY" ]; then
        cmd="$cmd --ai-key $GLM_API_KEY"
    elif [ -n "$OPENAI_API_KEY" ]; then
        cmd="$cmd --ai-key $OPENAI_API_KEY"
    fi
    
    cmd="$cmd --verbose"
    
    print_info "执行命令: $cmd"
    
    # 执行迁移
    if eval "$cmd"; then
        print_success "迁移完成！"
        
        print_info "后续步骤："
        print_info "1. cd $project_path"
        print_info "2. npm install"
        print_info "3. npm run build:prod"
        print_info "4. 检查并测试应用功能"
        
    else
        print_error "迁移失败，请查看错误信息"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Vue Element Admin 自动迁移脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项] [项目路径]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -d, --dry-run  预览模式，不实际修改文件"
    echo ""
    echo "示例:"
    echo "  $0                                    # 迁移当前目录"
    echo "  $0 /path/to/vue-element-admin         # 迁移指定目录"
    echo "  $0 --dry-run /path/to/project         # 预览模式"
    echo ""
    echo "环境变量:"
    echo "  DEEPSEEK_API_KEY  DeepSeek API Key（推荐）"
    echo "  GLM_API_KEY       GLM API Key"
    echo "  OPENAI_API_KEY    OpenAI API Key"
}

# 主函数
main() {
    local project_path="$(pwd)"
    local dry_run="false"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                dry_run="true"
                shift
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                project_path="$1"
                shift
                ;;
        esac
    done
    
    # 转换为绝对路径
    project_path=$(realpath "$project_path")
    
    echo "🚀 Vue Element Admin 自动迁移工具"
    echo "=================================="
    echo ""
    
    # 执行检查和迁移
    check_dependencies
    check_project "$project_path"
    setup_env
    run_migration "$project_path" "$dry_run"
}

# 运行主函数
main "$@"
