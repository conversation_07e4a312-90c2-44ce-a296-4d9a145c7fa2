#!/usr/bin/env node

/**
 * 测试运行脚本
 * 提供不同类型的测试运行选项
 */

const { spawn } = require('child_process');
const path = require('path');
const chalk = require('chalk');

const testTypes = {
  unit: {
    description: '单元测试 - 测试单个模块的功能',
    command: 'npm run test:unit'
  },
  integration: {
    description: '集成测试 - 测试 Sass 迁移模块的集成',
    command: 'npm run test:integration'
  },
  e2e: {
    description: '端到端测试 - 测试完整的迁移流程',
    command: 'npm run test:e2e'
  },
  sass: {
    description: 'Sass 相关测试 - 专门测试 Sass 迁移功能',
    command: 'npm run test:sass'
  },
  coverage: {
    description: '覆盖率测试 - 生成代码覆盖率报告',
    command: 'npm run test:coverage'
  },
  all: {
    description: '全部测试 - 运行所有测试套件',
    command: 'npm run test:all'
  },
  ci: {
    description: 'CI 测试 - 适用于持续集成环境',
    command: 'npm run test:ci'
  }
};

function printUsage() {
  console.log(chalk.bold.blue('\n🧪 Sass 迁移工具测试运行器\n'));
  
  console.log(chalk.bold('用法:'));
  console.log('  node scripts/run-tests.js [test-type] [options]\n');
  
  console.log(chalk.bold('测试类型:'));
  Object.entries(testTypes).forEach(([type, config]) => {
    console.log(`  ${chalk.green(type.padEnd(12))} ${config.description}`);
  });
  
  console.log(chalk.bold('\n选项:'));
  console.log('  --watch          监听模式');
  console.log('  --verbose        详细输出');
  console.log('  --bail           遇到错误时停止');
  console.log('  --help, -h       显示帮助信息');
  
  console.log(chalk.bold('\n示例:'));
  console.log('  node scripts/run-tests.js unit');
  console.log('  node scripts/run-tests.js sass --watch');
  console.log('  node scripts/run-tests.js all --verbose');
  console.log('  node scripts/run-tests.js coverage');
}

function runCommand(command, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue(`\n🚀 执行: ${command}\n`));
    
    const [cmd, ...args] = command.split(' ');
    const child = spawn(cmd, args, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..'),
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(chalk.green(`\n✅ 测试完成: ${command}`));
        resolve(code);
      } else {
        console.log(chalk.red(`\n❌ 测试失败: ${command} (退出码: ${code})`));
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      console.error(chalk.red(`\n💥 执行错误: ${error.message}`));
      reject(error);
    });
  });
}

async function runTests() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    printUsage();
    return;
  }
  
  const testType = args[0];
  const options = args.slice(1);
  
  if (!testType) {
    console.log(chalk.yellow('⚠️  请指定测试类型\n'));
    printUsage();
    process.exit(1);
  }
  
  if (!testTypes[testType]) {
    console.log(chalk.red(`❌ 未知的测试类型: ${testType}\n`));
    printUsage();
    process.exit(1);
  }
  
  const config = testTypes[testType];
  let command = config.command;
  
  // 处理选项
  if (options.includes('--watch')) {
    command += ' --watch';
  }
  
  if (options.includes('--verbose')) {
    command += ' --verbose';
  }
  
  if (options.includes('--bail')) {
    command += ' --bail';
  }
  
  try {
    console.log(chalk.bold.blue(`\n📋 运行测试类型: ${testType}`));
    console.log(chalk.gray(`描述: ${config.description}`));
    
    const startTime = Date.now();
    await runCommand(command);
    const endTime = Date.now();
    
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    console.log(chalk.green(`\n🎉 所有测试通过! 耗时: ${duration}s`));
    
    // 如果是覆盖率测试，提示查看报告
    if (testType === 'coverage') {
      console.log(chalk.blue('\n📊 覆盖率报告已生成:'));
      console.log(chalk.gray('  - HTML 报告: coverage/lcov-report/index.html'));
      console.log(chalk.gray('  - 文本报告: 已在上方显示'));
    }
    
  } catch (error) {
    console.error(chalk.red('\n💥 测试执行失败'));
    
    if (testType === 'e2e') {
      console.log(chalk.yellow('\n💡 端到端测试失败提示:'));
      console.log('  - 确保没有其他进程占用端口');
      console.log('  - 检查系统资源是否充足');
      console.log('  - 尝试单独运行: npm run test:e2e');
    }
    
    if (testType === 'all') {
      console.log(chalk.yellow('\n💡 全量测试失败提示:'));
      console.log('  - 可以分别运行各类测试定位问题');
      console.log('  - 单元测试: npm run test:unit');
      console.log('  - 集成测试: npm run test:integration');
      console.log('  - 端到端测试: npm run test:e2e');
    }
    
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error(chalk.red('\n💥 未捕获的异常:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('\n💥 未处理的 Promise 拒绝:'), reason);
  process.exit(1);
});

// 运行测试
runTests().catch((error) => {
  console.error(chalk.red('\n💥 测试运行器失败:'), error);
  process.exit(1);
});
