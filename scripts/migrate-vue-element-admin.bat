@echo off
setlocal enabledelayedexpansion

REM Vue Element Admin 自动迁移脚本 (Windows)
REM 使用方法: migrate-vue-element-admin.bat [项目路径]

set "PROJECT_PATH=%cd%"
set "DRY_RUN=false"

REM 解析参数
:parse_args
if "%~1"=="" goto :check_dependencies
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-d" (
    set "DRY_RUN=true"
    shift
    goto :parse_args
)
if "%~1"=="--dry-run" (
    set "DRY_RUN=true"
    shift
    goto :parse_args
)
set "PROJECT_PATH=%~1"
shift
goto :parse_args

:show_help
echo Vue Element Admin 自动迁移脚本
echo.
echo 使用方法:
echo   %~nx0 [选项] [项目路径]
echo.
echo 选项:
echo   -h, --help     显示帮助信息
echo   -d, --dry-run  预览模式，不实际修改文件
echo.
echo 示例:
echo   %~nx0                                    # 迁移当前目录
echo   %~nx0 C:\path\to\vue-element-admin       # 迁移指定目录
echo   %~nx0 --dry-run C:\path\to\project       # 预览模式
echo.
echo 环境变量:
echo   DEEPSEEK_API_KEY  DeepSeek API Key（推荐）
echo   GLM_API_KEY       GLM API Key
echo   OPENAI_API_KEY    OpenAI API Key
goto :eof

:check_dependencies
echo [INFO] 检查依赖...

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js
    exit /b 1
)

REM 检查 npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm 未安装，请先安装 npm
    exit /b 1
)

echo [SUCCESS] 依赖检查通过

:check_project
echo [INFO] 检查项目: %PROJECT_PATH%

if not exist "%PROJECT_PATH%" (
    echo [ERROR] 项目目录不存在: %PROJECT_PATH%
    exit /b 1
)

if not exist "%PROJECT_PATH%\package.json" (
    echo [ERROR] 未找到 package.json 文件
    exit /b 1
)

REM 检查是否为 Vue Element Admin 项目
findstr /c:"vue-element-admin" "%PROJECT_PATH%\package.json" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 未检测到 Vue Element Admin 项目，将使用通用迁移配置
) else (
    echo [SUCCESS] 检测到 Vue Element Admin 项目
)

:setup_env
echo [INFO] 设置环境变量...

REM 检查 AI API Key
if defined DEEPSEEK_API_KEY (
    echo [SUCCESS] 检测到 DeepSeek API Key
    set "AI_PROVIDER=deepseek"
) else if defined GLM_API_KEY (
    echo [SUCCESS] 检测到 GLM API Key
    set "AI_PROVIDER=glm"
) else if defined OPENAI_API_KEY (
    echo [SUCCESS] 检测到 OpenAI API Key
    set "AI_PROVIDER=openai"
) else (
    echo [WARNING] 未检测到 AI API Key，将跳过 AI 修复步骤
    echo [INFO] 可以设置以下环境变量之一：
    echo [INFO]   set DEEPSEEK_API_KEY=your_key
    echo [INFO]   set GLM_API_KEY=your_key
    echo [INFO]   set OPENAI_API_KEY=your_key
)

:run_migration
echo [INFO] 开始执行迁移...

REM 构建命令
set "CMD=node "%~dp0..\bin\vue-migrator.js" auto "%PROJECT_PATH%""

if "%DRY_RUN%"=="true" (
    set "CMD=!CMD! --dry-run"
    echo [INFO] 运行预览模式（不会实际修改文件）
)

if defined DEEPSEEK_API_KEY (
    set "CMD=!CMD! --ai-key %DEEPSEEK_API_KEY%"
) else if defined GLM_API_KEY (
    set "CMD=!CMD! --ai-key %GLM_API_KEY%"
) else if defined OPENAI_API_KEY (
    set "CMD=!CMD! --ai-key %OPENAI_API_KEY%"
)

set "CMD=!CMD! --verbose"

echo [INFO] 执行命令: !CMD!

REM 执行迁移
!CMD!
if errorlevel 1 (
    echo [ERROR] 迁移失败，请查看错误信息
    exit /b 1
)

echo [SUCCESS] 迁移完成！
echo.
echo [INFO] 后续步骤：
echo [INFO] 1. cd /d "%PROJECT_PATH%"
echo [INFO] 2. npm install
echo [INFO] 3. npm run build:prod
echo [INFO] 4. 检查并测试应用功能

goto :eof

:main
echo 🚀 Vue Element Admin 自动迁移工具
echo ==================================
echo.

call :check_dependencies
if errorlevel 1 exit /b 1

call :check_project
if errorlevel 1 exit /b 1

call :setup_env

call :run_migration
if errorlevel 1 exit /b 1

goto :eof

REM 调用主函数
call :main
