我正在设计一个 Vue 2 到 Vue 3 的规模化迁移方案，我需要你根据我的 task 编写迁移脚本。相关信息：

- aup-admin-ui 是 Vue 2 迁移的代码库
- vue-elment-admin 是 aup-admin-ui  原来的模板应用
- vue3-element-plus-admin 是新的工程模板（你要工作在这个工程中）
- migrate-cli 是未来我要编写的迁移脚本，将使用 gogocode 进行代码迁移，因此需要对齐原来的工程。

我的目标技术栈是： TypeScript/Javascript + vuex + vue router 4 + element-plus


Todos: 

- [ ] Vue Router 4 迁移
- [ ] Vite 脚本 ？ `@vitejs/plugin-vue`


## ✅ 1. 使用 Gogocode 转换 `package.json` 中 Vue 相关依赖版本

**目标**：将 `vue`, `vue-template-compiler`, `vue-router`, `vuex` 等升级为 Vue 3 兼容版本。

**实现建议**：

* 使用 `gogocode` 修改 JSON 文件结构（也可直接用 JS 操作）
* 或使用 Node 脚本处理 `package.json`

**示例代码**（Node）：

```js
const fs = require('fs');
const pkg = require('./package.json');

pkg.dependencies.vue = '^3.4.0';
delete pkg.dependencies['vue-template-compiler'];
pkg.devDependencies['@vue/compiler-sfc'] = '^3.4.0';

fs.writeFileSync('./package.json', JSON.stringify(pkg, null, 2));
```

---

## ✅ 2. 检查每个依赖是否存在 Vue 3 的兼容版本（可选）

**目标**：提前排查不兼容插件，有没有可能结合 AI 检查哪些是 Vue 的组件，如果配置了 AI 的话。

**做法**：

* 使用 `npm view <pkg> peerDependencies` 查看是否支持 Vue 3
* 可结合 [`npm-check-updates`](https://www.npmjs.com/package/npm-check-updates)

**示例代码**：

```bash
npx npm-check-updates -u
npm install
```

或检查某依赖的最新版本信息：

```bash
npm view vue-router versions
```

---

## ✅ 3. 批量使用 Gogocode 将 `.vue` 和 `.js` 文件迁移到 Vue 3

**目标**：将 Vue 2 的语法（如 `this.$refs`, `this.$emit`, `options API`）迁移为 Composition API 等。

**做法**：

* 使用 [Gogocode AST 转换脚本](https://github.com/thx/gogocode)
* 可以用现成的 Vue 2 ➝ 3 preset，或自己编写转换器

```js
const elementTransform = require('gogocode-plugin-element')

const options = {
    outRootPath: path.dirname(targetDir, '.'),
}

const vueResult = vueTransform.transform({
    source,
    path: filePath,
    options
}, {
    gogocode: require('gogocode')
}, {})

```

---

## ✅ 4. 记录转换失败的文件

**目标**：识别失败文件，供后续 AI 修复


## ✅ 5. 使用 AI 库（如 ChatGPT API）修复失败文件

**目标**：自动修复 Gogocode 无法迁移的边缘文件

**推荐**：

* 使用 Deepssek 等自动调用修复失败代码
* Prompt 应包括 Vue 2 和目标 Vue 3 的背景提示

**示例（伪代码）**：

```js
import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

const { text } = await generateText({
    model: openai("gpt-4o"),
    prompt: prompt,
    maxTokens: 4000,
    })

///     
```



---

## ✅ 6. 使用 ESLint 自动修复格式和语法

**目标**：统一格式，修复可能的语法问题


## ✅ 7. 构建项目并使用 AI 修复构建错误

**目标**：完成构建，进一步修复遗留问题（如 TypeError, undefined 等）

**做法**：

* 执行 `vite build` 或 `webpack build`
* 捕捉构建错误输出，结合 AI 自动修复

## ✨ Bonus：整合为自动化 CLI 工具

你可以封装上述流程为一个 CLI 脚本，例如使用 Node + Commander：

```bash
npx vue2to3-migrator --input ./src --ai --eslint --build
```

## 🚀 CLI 使用指南

### 构建错误修复工具（新增）

BuildFixer 是一个独立的构建错误自动修复工具，可以智能识别和修复 Vue 项目的构建错误。

```bash
# 修复当前项目的构建错误
build-fixer fix

# 修复指定项目的构建错误
build-fixer fix /path/to/project

# 预览模式（不实际修改文件）
build-fixer fix --dry-run

# 显示详细信息
build-fixer fix --verbose

# 使用自定义构建命令
build-fixer fix --build-command "yarn build"

# 跳过 AI 修复，只使用规则修复
build-fixer fix --skip-ai

# 初始化配置文件
build-fixer init

# 查看配置信息
build-fixer config
```

**BuildFixer 特性：**
- 🔧 自动错误修复：智能识别和修复常见的构建错误
- 🤖 AI 驱动：使用 AI 技术处理复杂的错误场景
- 📊 进度跟踪：实时显示修复进度和统计信息
- ⚙️ 灵活配置：支持通过配置文件自定义行为
- 🔍 预览模式：支持 dry-run 模式预览修复操作

详见：[BuildFixer 使用指南](./docs/build-fixer.md)

### 自动迁移模式（推荐）

```bash
# 自动迁移当前目录项目
node bin/vue-migrator.js auto

# 自动迁移指定项目
node bin/vue-migrator.js auto /path/to/project

# 启用 ESLint 修复（默认禁用）
node bin/vue-migrator.js auto --eslint

# 预览模式，不实际修改
node bin/vue-migrator.js auto --dry-run

# 指定 AI API Key
node bin/vue-migrator.js auto --ai-key <your-api-key>
```

### 传统迁移模式

```bash
# 执行完整迁移（7个步骤）
node bin/vue-migrator.js migrate

# 启用 ESLint 修复（默认禁用）
node bin/vue-migrator.js migrate --eslint

# 跳过 AI 修复
node bin/vue-migrator.js migrate --skip-ai

# 跳过构建测试
node bin/vue-migrator.js migrate --skip-build
```

### 单步执行

```bash
# 执行指定步骤 (1-7)
node bin/vue-migrator.js step 1  # 升级 package.json
node bin/vue-migrator.js step 3  # 批量迁移代码
node bin/vue-migrator.js step 6  # ESLint 修复
```

### 参数说明

- `--eslint`: 启用 ESLint 自动修复（默认禁用，避免大规模代码变更）
- `--skip-ai`: 跳过 AI 修复步骤
- `--skip-sass-migration`: 跳过 Sass 语法迁移步骤
- `--skip-build`: 跳过构建测试步骤
- `--skip-dependency-check`: 跳过依赖兼容性检查
- `--dry-run`: 预览模式，不实际修改文件
- `--verbose`: 显示详细信息
- `--ai-key <key>`: 指定 AI API Key（支持 DeepSeek/GLM/OpenAI）

### 迁移步骤说明

1. **升级 package.json 依赖** - 将 Vue 相关依赖升级到 Vue 3 版本
2. **检查依赖兼容性** - 检查第三方依赖是否支持 Vue 3
3. **批量迁移代码文件** - 使用 Gogocode 转换 .vue 和 .js 文件
4. **记录失败文件** - 记录转换失败的文件供后续处理
5. **AI 修复失败文件** - 使用 AI 自动修复转换失败的文件
6. **Sass 语法迁移** - 将 @import 转换为 @use 语法（自动检测 sass-migrator）
7. **ESLint 自动修复** - 运行 ESLint 修复格式和语法问题（默认禁用）
8. **构建项目并修复错误** - 尝试构建项目并使用 AI 修复构建错误

### Sass 语法迁移

工具支持自动迁移 Sass/SCSS 文件的语法，将废弃的 `@import` 转换为现代的 `@use` 语法。提供基础版和增强版两种模式：

#### 增强版迁移器（推荐）

```bash
# 使用增强版迁移器，包含完整的优化功能
sass-migrator-vue enhanced /path/to/your/project

# 预览模式
sass-migrator-vue enhanced /path/to/your/project --dry-run

# 自定义配置
sass-migrator-vue enhanced /path/to/your/project \
  --no-architecture-refactor \
  --build-command "npm run build:prod"
```

**增强版特性：**
- 🎯 智能路径解析（处理 ~ 别名、Vite 配置集成）
- 🏗️ 架构重构（自动生成桶文件、检测循环依赖）
- 🎨 Element Plus 专项迁移（Element UI → Element Plus）
- 🔍 智能错误诊断（提供具体修复建议）
- ⚙️ Vite 配置优化（自动配置 loadPaths）
- ✅ 迁移验证（构建测试、完整性检查）

#### 基础版迁移器

```bash
# 基础语法转换
sass-migrator-vue /path/to/your/project

# 使用增强功能
sass-migrator-vue /path/to/your/project --enhanced

# 恢复备份
sass-migrator-vue restore-backups /path/to/your/project
```

详见：
- [增强版 Sass 迁移指南](./docs/sass-migration-enhanced.md)
- [基础 Sass 迁移指南](src/sass/migrators/README.md)

### 使用建议

- **首次迁移**: 建议使用 `auto` 模式，不启用 ESLint
- **代码格式化**: 迁移完成后再手动运行 ESLint 或使用 `--eslint` 参数
- **AI 辅助**: 配置 AI API Key 可以获得更好的修复效果
- **Sass 迁移**: 如果项目使用 Sass，确保安装了 `sass-migrator` 工具
- **备份项目**: 迁移前会自动备份项目文件
