const path = require('path');
const fs = require('fs-extra');
const { spawn } = require('child_process');

describe('端到端迁移测试', () => {
  let tempDir;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('e2e-migration-');
  });

  describe('基础迁移场景', () => {
    test('应该成功迁移简单的 Vue 项目', async () => {
      // 创建一个真实的项目结构
      await global.testUtils.createTestProject(tempDir);
      
      // 执行迁移
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Sass 迁移完成');
    }, 60000);

    test('应该处理 Element UI 项目迁移', async () => {
      await global.testUtils.createElementUIProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Element Plus 主题迁移');
    }, 60000);

    test('应该处理循环依赖项目', async () => {
      await global.testUtils.createCircularDependencyProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('发现');
      expect(result.stdout).toContain('循环依赖');
    }, 60000);
  });

  describe('复杂项目场景', () => {
    test('应该处理大型项目结构', async () => {
      // 创建复杂的项目结构
      await createComplexProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced', '--verbose']);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('架构重构');
    }, 90000);

    test('应该处理混合路径项目', async () => {
      await global.testUtils.createComplexPathProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('路径解析');
    }, 60000);
  });

  describe('错误处理场景', () => {
    test('应该处理无效项目路径', async () => {
      const invalidPath = path.join(tempDir, 'non-existent');
      
      const result = await runMigrationCLI(invalidPath, ['--enhanced']);
      
      expect(result.exitCode).not.toBe(0);
      expect(result.stderr).toContain('package.json');
    }, 30000);

    test('应该处理权限错误', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      // 模拟权限错误（在实际环境中可能需要不同的方法）
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
      
      // 在预览模式下应该成功
      expect(result.exitCode).toBe(0);
    }, 60000);
  });

  describe('CLI 选项测试', () => {
    test('应该支持 --verbose 选项', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced', '--verbose']);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout.length).toBeGreaterThan(1000); // 详细输出应该更长
    }, 60000);

    test('应该支持功能禁用选项', async () => {
      await global.testUtils.createElementUIProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, [
        '--dry-run',
        '--enhanced',
        '--no-element-plus',
        '--no-architecture-refactor'
      ]);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).not.toContain('Element Plus 迁移');
      expect(result.stdout).not.toContain('架构重构');
    }, 60000);

    test('应该支持自定义构建命令', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, [
        '--dry-run',
        '--enhanced',
        '--build-command',
        'npm run build:prod'
      ]);
      
      expect(result.exitCode).toBe(0);
    }, 60000);
  });

  describe('实际文件修改测试', () => {
    test('应该实际修改文件（非预览模式）', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      // 记录原始内容
      const mainScssPath = path.join(tempDir, 'src/styles/main.scss');
      const originalContent = await fs.readFile(mainScssPath, 'utf8');
      expect(originalContent).toContain('@import');
      
      // 执行实际迁移
      const result = await runMigrationCLI(tempDir, ['--enhanced']);
      
      expect(result.exitCode).toBe(0);
      
      // 检查文件是否被修改
      const modifiedContent = await fs.readFile(mainScssPath, 'utf8');
      expect(modifiedContent).not.toBe(originalContent);
      expect(modifiedContent).toContain('@use');
    }, 90000);

    test('应该创建备份文件', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--enhanced']);
      
      expect(result.exitCode).toBe(0);
      
      // 检查备份文件
      const backupFiles = await global.testUtils.getFiles(tempDir, '**/*.sass-backup');
      expect(backupFiles.length).toBeGreaterThan(0);
    }, 90000);

    test('应该生成配置文件', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--enhanced']);
      
      expect(result.exitCode).toBe(0);
      
      // 检查生成的文件
      const barrelFile = path.join(tempDir, 'src/styles/index.scss');
      expect(await fs.pathExists(barrelFile)).toBe(true);
      
      const viteConfig = path.join(tempDir, 'vite.config.js');
      if (await fs.pathExists(viteConfig)) {
        const content = await fs.readFile(viteConfig, 'utf8');
        expect(content).toContain('loadPaths');
      }
    }, 90000);
  });

  describe('迁移验证', () => {
    test('迁移后的项目应该通过验证', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      // 执行迁移
      const migrationResult = await runMigrationCLI(tempDir, ['--enhanced']);
      expect(migrationResult.exitCode).toBe(0);
      
      // 验证迁移结果
      const validation = await global.testUtils.validateMigrationResult(tempDir);
      
      expect(validation.hasUseStatements).toBe(true);
      expect(validation.hasBarrelFile).toBe(true);
      expect(validation.errors.length).toBe(0);
    }, 120000);

    test('应该生成迁移报告', async () => {
      await global.testUtils.createElementUIProject(tempDir);
      
      const result = await runMigrationCLI(tempDir, ['--enhanced']);
      
      expect(result.exitCode).toBe(0);
      
      // 检查迁移报告
      const reportFile = path.join(tempDir, 'element-plus-migration-report.md');
      expect(await fs.pathExists(reportFile)).toBe(true);
      
      const reportContent = await fs.readFile(reportFile, 'utf8');
      expect(reportContent).toContain('# Element Plus 迁移报告');
    }, 90000);
  });

  describe('性能测试', () => {
    test('应该在合理时间内完成大项目迁移', async () => {
      // 创建包含多个文件的大项目
      await createLargeProject(tempDir);
      
      const startTime = Date.now();
      const result = await runMigrationCLI(tempDir, ['--dry-run', '--enhanced']);
      const endTime = Date.now();
      
      expect(result.exitCode).toBe(0);
      expect(endTime - startTime).toBeLessThan(60000); // 应该在60秒内完成
    }, 90000);
  });

  // 辅助函数
  async function runMigrationCLI(projectPath, args = []) {
    return new Promise((resolve) => {
      const cliPath = path.join(__dirname, '../../bin/sass-migrator.js');
      const child = spawn('node', [cliPath, projectPath, ...args], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({
          exitCode: code,
          stdout,
          stderr
        });
      });

      // 设置超时
      setTimeout(() => {
        child.kill();
        resolve({
          exitCode: -1,
          stdout,
          stderr: stderr + '\nTimeout: Process killed after timeout'
        });
      }, 120000);
    });
  }

  async function createComplexProject(projectPath) {
    const structure = {
      'package.json': {
        name: 'complex-project',
        dependencies: {
          vue: '^3.0.0',
          'element-ui': '^2.15.0'
        },
        devDependencies: {
          vite: '^4.0.0',
          sass: '^1.50.0'
        }
      },
      'vite.config.js': `import { defineConfig } from 'vite';
export default defineConfig({
  plugins: []
});`,
      'src/styles/utils/_variables.scss': '$primary: #409eff; $secondary: #909399;',
      'src/styles/utils/_mixins.scss': '@import "./variables"; @mixin btn { padding: 8px; }',
      'src/styles/utils/_functions.scss': '@function rem($px) { @return $px / 16px * 1rem; }',
      'src/styles/components/_button.scss': '@import "../utils/variables"; .btn { color: $primary; }',
      'src/styles/components/_card.scss': '@import "../utils/mixins"; .card { @include btn; }',
      'src/styles/themes/_dark.scss': '@import "../utils/variables"; $dark-bg: #1a1a1a;',
      'src/styles/themes/_light.scss': '@import "../utils/variables"; $light-bg: #ffffff;',
      'src/styles/main.scss': `
        @import "~element-ui/packages/theme-chalk/src/index";
        @import "./utils/variables";
        @import "./components/button";
        @import "./components/card";
      `,
      'src/components/Button.vue': `
        <template><button><slot/></button></template>
        <style lang="scss">
        @import "../styles/utils/variables";
        .button { background: $primary; }
        </style>
      `
    };

    return global.testUtils.createTestProject(projectPath, structure);
  }

  async function createLargeProject(projectPath) {
    const baseStructure = await createComplexProject(projectPath);
    
    // 添加更多文件
    for (let i = 1; i <= 20; i++) {
      const componentPath = `src/components/Component${i}.vue`;
      const stylePath = `src/styles/components/_component${i}.scss`;
      
      await fs.writeFile(path.join(projectPath, componentPath), `
        <template><div class="component-${i}">Component ${i}</div></template>
        <style lang="scss">
        @import "../styles/utils/variables";
        .component-${i} { color: $primary; }
        </style>
      `);
      
      await fs.writeFile(path.join(projectPath, stylePath), `
        @import "../utils/variables";
        @import "../utils/mixins";
        .component-${i} {
          color: $primary;
          @include btn;
        }
      `);
    }
    
    return projectPath;
  }
});
