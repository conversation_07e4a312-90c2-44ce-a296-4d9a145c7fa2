const fs = require('fs-extra');
const path = require('path');
const os = require('os');

/**
 * 测试工具类
 * 提供创建临时项目、模拟文件等测试辅助功能
 */
class TestUtils {
  constructor() {
    this.tempDirs = [];
  }

  /**
   * 创建临时测试目录
   */
  async createTempDir(prefix = 'sass-test-') {
    const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), prefix));
    this.tempDirs.push(tempDir);
    return tempDir;
  }

  /**
   * 清理所有临时目录
   */
  async cleanup() {
    for (const dir of this.tempDirs) {
      try {
        await fs.remove(dir);
      } catch (error) {
        console.warn(`Failed to remove temp dir ${dir}:`, error.message);
      }
    }
    this.tempDirs = [];
  }

  /**
   * 创建测试项目结构
   */
  async createTestProject(projectPath, structure = {}) {
    await fs.ensureDir(projectPath);
    
    // 默认项目结构
    const defaultStructure = {
      'package.json': {
        name: 'test-project',
        version: '1.0.0',
        dependencies: {
          vue: '^3.0.0',
          'element-ui': '^2.15.0'
        },
        devDependencies: {
          vite: '^4.0.0',
          sass: '^1.50.0'
        }
      },
      'vite.config.js': `import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()]
});`,
      'src/styles/variables.scss': `$primary-color: #409eff;
$font-size-base: 14px;
$border-radius: 4px;`,
      'src/styles/mixins.scss': `@import "./variables";

@mixin button-style {
  padding: 8px 16px;
  border-radius: $border-radius;
  color: $primary-color;
}`,
      'src/styles/main.scss': `@import "~element-ui/packages/theme-chalk/src/index";
@import "./variables";
@import "./mixins";

.app {
  font-size: $font-size-base;
  
  .button {
    @include button-style;
  }
}`,
      'src/components/Button.vue': `<template>
  <button class="custom-button">
    <slot />
  </button>
</template>

<style lang="scss">
@import "../styles/variables";

.custom-button {
  background: $primary-color;
  border: none;
  padding: 8px 16px;
}
</style>`
    };

    const finalStructure = { ...defaultStructure, ...structure };
    
    for (const [filePath, content] of Object.entries(finalStructure)) {
      // 跳过undefined内容的文件
      if (content === undefined) {
        continue;
      }
      
      const fullPath = path.join(projectPath, filePath);
      await fs.ensureDir(path.dirname(fullPath));
      
      if (typeof content === 'object') {
        await fs.writeJson(fullPath, content, { spaces: 2 });
      } else {
        await fs.writeFile(fullPath, content, 'utf8');
      }
    }
    
    return projectPath;
  }

  /**
   * 创建 Element UI 项目
   */
  async createElementUIProject(projectPath) {
    return this.createTestProject(projectPath, {
      'src/styles/element-variables.scss': `$--color-primary: #1890ff;
$--color-success: #52c41a;
$--font-size-base: 14px;`,
      'src/styles/element.scss': `@import "~element-ui/packages/theme-chalk/src/index";
@import "./element-variables";`,
      'src/main.js': `import { createApp } from 'vue';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './styles/element.scss';

const app = createApp({});
app.use(ElementUI);`
    });
  }

  /**
   * 创建循环依赖项目
   */
  async createCircularDependencyProject(projectPath) {
    return this.createTestProject(projectPath, {
      'src/styles/a.scss': `@import "./b";
$color-a: red;`,
      'src/styles/b.scss': `@import "./c";
$color-b: blue;`,
      'src/styles/c.scss': `@import "./a";
$color-c: green;`,
      'src/styles/self-reference.scss': `@import "./self-reference";
$self-color: yellow;`
    });
  }

  /**
   * 创建复杂路径项目
   */
  async createComplexPathProject(projectPath) {
    return this.createTestProject(projectPath, {
      'vite.config.js': `import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@styles': path.resolve(__dirname, 'src/styles')
    }
  }
});`,
      'src/styles/themes/dark.scss': `$dark-bg: #1a1a1a;
$dark-text: #ffffff;`,
      'src/styles/components/button.scss': `@import "~bootstrap/scss/bootstrap";
@import "../themes/dark";
@import "@styles/variables";

.btn-custom {
  background: $dark-bg;
  color: $dark-text;
}`
    });
  }

  /**
   * 读取文件内容
   */
  async readFile(filePath) {
    return fs.readFile(filePath, 'utf8');
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(filePath) {
    return fs.pathExists(filePath);
  }

  /**
   * 获取目录下的所有文件
   */
  async getFiles(dirPath, pattern = '**/*') {
    const { glob } = require('glob');
    try {
      const files = await glob(pattern, { cwd: dirPath, absolute: true });
      return files;
    } catch (err) {
      throw err;
    }
  }

  /**
   * 比较文件内容
   */
  async compareFiles(file1, file2) {
    const content1 = await this.readFile(file1);
    const content2 = await this.readFile(file2);
    return content1 === content2;
  }

  /**
   * 检查文件是否包含特定内容
   */
  async fileContains(filePath, searchText) {
    const content = await this.readFile(filePath);
    return content.includes(searchText);
  }

  /**
   * 检查文件是否匹配正则表达式
   */
  async fileMatches(filePath, regex) {
    const content = await this.readFile(filePath);
    return regex.test(content);
  }

  /**
   * 创建模拟的 Vite 配置
   */
  createMockViteConfig(options = {}) {
    return {
      plugins: options.plugins || [],
      resolve: {
        alias: options.alias || { '@': '/src' }
      },
      css: {
        preprocessorOptions: {
          scss: {
            loadPaths: options.loadPaths || [],
            additionalData: options.additionalData || ''
          }
        }
      },
      ...options
    };
  }

  /**
   * 创建模拟的 package.json
   */
  createMockPackageJson(options = {}) {
    return {
      name: options.name || 'test-project',
      version: options.version || '1.0.0',
      dependencies: {
        vue: '^3.0.0',
        ...options.dependencies
      },
      devDependencies: {
        vite: '^4.0.0',
        sass: '^1.50.0',
        ...options.devDependencies
      },
      ...options
    };
  }

  /**
   * 模拟命令行执行
   */
  mockExecSync(command, result = '', shouldThrow = false) {
    const originalExecSync = require('child_process').execSync;
    
    return jest.spyOn(require('child_process'), 'execSync').mockImplementation((cmd) => {
      if (cmd.includes(command)) {
        if (shouldThrow) {
          const error = new Error(result);
          error.stderr = result;
          throw error;
        }
        return result;
      }
      return originalExecSync(cmd);
    });
  }

  /**
   * 创建测试用的错误输出
   */
  createMockErrorOutput(errorType = 'undefined-variable') {
    const errorOutputs = {
      'undefined-variable': 'Error: Undefined variable: $primary-color\n  on line 5 of src/styles/main.scss',
      'module-loop': 'Error: Module loop: this module is already being loaded\n  in src/styles/circular.scss',
      'file-not-found': 'Error: Could not find Sass file at: ~element-ui/packages/theme-chalk/src/index\n  in src/styles/main.scss',
      'invalid-import': 'Warning: @import rules are deprecated and will be removed in Dart Sass 3.0.0\n  in src/styles/legacy.scss'
    };
    
    return errorOutputs[errorType] || errorOutputs['undefined-variable'];
  }

  /**
   * 验证迁移结果
   */
  async validateMigrationResult(projectPath, expectations = {}) {
    const results = {
      hasUseStatements: false,
      hasImportStatements: false,
      hasBarrelFile: false,
      hasViteConfig: false,
      hasElementPlusConfig: false,
      errors: []
    };

    try {
      // 检查是否有 @use 语句
      const scssFiles = await this.getFiles(projectPath, '**/*.scss');
      for (const file of scssFiles) {
        const content = await this.readFile(file);
        if (content.includes('@use')) {
          results.hasUseStatements = true;
        }
        if (content.includes('@import')) {
          results.hasImportStatements = true;
        }
      }

      // 检查桶文件
      const barrelFile = path.join(projectPath, 'src/styles/index.scss');
      results.hasBarrelFile = await this.fileExists(barrelFile);

      // 检查 Vite 配置
      const viteConfig = path.join(projectPath, 'vite.config.js');
      if (await this.fileExists(viteConfig)) {
        results.hasViteConfig = true;
        const content = await this.readFile(viteConfig);
        if (content.includes('loadPaths')) {
          results.hasOptimizedViteConfig = true;
        }
      }

      // 检查 Element Plus 配置
      const elementConfig = path.join(projectPath, 'src/styles/element/index.scss');
      results.hasElementPlusConfig = await this.fileExists(elementConfig);

    } catch (error) {
      results.errors.push(error.message);
    }

    return results;
  }
}

module.exports = TestUtils;
