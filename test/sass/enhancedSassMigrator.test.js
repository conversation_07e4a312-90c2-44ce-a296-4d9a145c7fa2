const path = require('path');
const fs = require('fs-extra');
const EnhancedSassMigrator = require('../../src/sass/migrators/enhancedSassMigrator');

describe('EnhancedSassMigrator', () => {
  let tempDir;
  let migrator;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('enhanced-migrator-');
    migrator = new EnhancedSassMigrator(tempDir, {
      verbose: false,
      dryRun: true // 默认使用预览模式避免实际修改
    });
  });

  describe('初始化', () => {
    test('应该正确初始化增强版迁移器', () => {
      expect(migrator.projectPath).toBe(tempDir);
      expect(migrator.pathResolver).toBeDefined();
      expect(migrator.architectureRefactor).toBeDefined();
      expect(migrator.elementPlusMigrator).toBeDefined();
      expect(migrator.errorDiagnostic).toBeDefined();
      expect(migrator.viteOptimizer).toBeDefined();
    });

    test('应该初始化统计信息', () => {
      expect(migrator.stats.totalFiles).toBe(0);
      expect(migrator.stats.processedFiles).toBe(0);
      expect(migrator.stats.errors).toEqual([]);
      expect(migrator.stats.optimizations).toEqual([]);
    });
  });

  describe('预检查', () => {
    test('应该检查项目结构', async () => {
      await global.testUtils.createTestProject(tempDir);

      await migrator.preCheck();

      expect(migrator.stats.totalFiles).toBeGreaterThan(0);
    });

    test('应该处理没有 package.json 的项目', async () => {
      await expect(migrator.preCheck()).rejects.toThrow('未找到 package.json 文件');
    });

    test('应该处理没有 Sass 文件的项目', async () => {
      await global.testUtils.createTestProject(tempDir, {
        'package.json': { name: 'test' },
        'src/main.js': 'console.log("hello");',
        // 覆盖默认的scss文件
        'src/styles/variables.scss': undefined,
        'src/styles/mixins.scss': undefined,
        'src/styles/main.scss': undefined
      });

      // 删除默认创建的scss文件
      const stylesDir = path.join(tempDir, 'src/styles');
      if (await fs.pathExists(stylesDir)) {
        await fs.remove(stylesDir);
      }

      await migrator.preCheck();

      expect(migrator.stats.totalFiles).toBe(0);
    });
  });

  describe('项目结构检查', () => {
    test('应该检测 Vue 项目', async () => {
      await global.testUtils.createTestProject(tempDir);

      await expect(migrator.checkProjectStructure()).resolves.not.toThrow();
    });

    test('应该警告非 Vue 项目', async () => {
      const packageJson = { name: 'test', dependencies: {} };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      // 应该不抛出错误，但会有警告
      await expect(migrator.checkProjectStructure()).resolves.not.toThrow();
    });

    test('应该检测 Vite 项目', async () => {
      const packageJson = {
        name: 'test',
        dependencies: { vue: '^3.0.0' },
        devDependencies: { vite: '^4.0.0' }
      };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      await expect(migrator.checkProjectStructure()).resolves.not.toThrow();
    });
  });

  describe('sass-migrator 可用性检查', () => {
    test('应该检测 sass-migrator 是否可用', async () => {
      // 模拟 sass-migrator 可用
      const mockExec = global.testUtils.mockExecSync('sass-migrator --version', 'sass-migrator 1.0.0');

      const isAvailable = await migrator.isSassMigratorAvailable();
      expect(isAvailable).toBe(true);

      mockExec.mockRestore();
    });

    test('应该处理 sass-migrator 不可用的情况', async () => {
      // 模拟 sass-migrator 不可用
      const mockExec = global.testUtils.mockExecSync('sass-migrator --version', 'command not found', true);

      const isAvailable = await migrator.isSassMigratorAvailable();
      expect(isAvailable).toBe(false);

      mockExec.mockRestore();
    });
  });

  describe('内置迁移逻辑', () => {
    test('应该检测需要迁移的文件', () => {
      const content = '@import "variables";';
      expect(migrator.needsMigration(content)).toBe(true);

      const modernContent = '@use "variables" as *;';
      expect(migrator.needsMigration(modernContent)).toBe(false);
    });

    test('应该转换 @import 为 @use', () => {
      const content = '@import "variables";';
      const filePath = path.join(tempDir, 'test.scss');

      const result = migrator.convertImportsToUse(content, filePath);
      expect(result).toContain('@use');
      expect(result).not.toContain('@import');
    });

    test('应该修复路径', () => {
      const content = '@use "~element-ui/packages/theme-chalk/src/index" as *;';
      const filePath = path.join(tempDir, 'test.scss');

      const result = migrator.fixPaths(content, filePath);
      expect(result).toContain('element-plus');
    });
  });

  describe('文件备份', () => {
    test('应该创建备份文件', async () => {
      const testFile = path.join(tempDir, 'test.scss');
      await fs.writeFile(testFile, '$color: red;');

      await migrator.backupFile(testFile);

      const backupFile = `${testFile}.sass-backup`;
      expect(await fs.pathExists(backupFile)).toBe(true);

      const backupContent = await fs.readFile(backupFile, 'utf8');
      expect(backupContent).toBe('$color: red;');
    });
  });

  describe('构建测试', () => {
    test('应该尝试构建项目', async () => {
      // 模拟成功的构建
      const mockExec = global.testUtils.mockExecSync('npm run build', '');

      const buildErrors = await migrator.tryBuild();
      expect(buildErrors).toBe('');

      mockExec.mockRestore();
    });

    test('应该捕获构建错误', async () => {
      // 模拟失败的构建
      const errorMessage = 'Error: Undefined variable: $primary-color';
      const mockExec = global.testUtils.mockExecSync('npm run build', errorMessage, true);

      const buildErrors = await migrator.tryBuild();
      expect(buildErrors).toContain('Undefined variable');

      mockExec.mockRestore();
    });
  });

  describe('剩余导入检查', () => {
    test('应该检查剩余的 @import 语句', async () => {
      await global.testUtils.createTestProject(tempDir);

      const remainingImports = await migrator.checkRemainingImports();
      expect(remainingImports.length).toBeGreaterThan(0);
    });

    test('应该处理已完全迁移的项目', async () => {
      await global.testUtils.createTestProject(tempDir, {
        'src/styles/main.scss': '@use "variables" as *;',
        'src/styles/variables.scss': '$primary-color: #409eff;',
        'src/styles/mixins.scss': '@use "./variables" as *;'
      });

      const remainingImports = await migrator.checkRemainingImports();
      expect(remainingImports.length).toBe(0);
    });
  });

  describe('完整迁移流程', () => {
    test('应该执行完整的迁移流程（预览模式）', async () => {
      await global.testUtils.createTestProject(tempDir);

      const report = await migrator.migrate();

      expect(report.summary).toBeDefined();
      expect(report.summary.totalFiles).toBeGreaterThan(0);
      expect(report.optimizations).toBeDefined();
      expect(report.timestamp).toBeDefined();
    });

    test('应该处理 Element UI 项目', async () => {
      await global.testUtils.createElementUIProject(tempDir);

      const report = await migrator.migrate();

      expect(report.optimizations.some(opt => opt.type === 'element-plus')).toBe(true);
    });

    test('应该处理循环依赖项目', async () => {
      await global.testUtils.createCircularDependencyProject(tempDir);

      const report = await migrator.migrate();

      expect(report.optimizations.some(opt => opt.type === 'architecture')).toBe(true);
    });
  });

  describe('错误处理', () => {
    test('应该处理文件迁移错误', async () => {
      const testFile = path.join(tempDir, 'test.scss');
      await fs.writeFile(testFile, '@import "variables";');

      // 模拟文件读取错误
      jest.spyOn(fs, 'readFile').mockRejectedValueOnce(new Error('Permission denied'));

      await migrator.migrateFileInternal(testFile);

      expect(migrator.stats.errorFiles).toBe(1);
      expect(migrator.stats.errors.length).toBe(1);
    });

    test('应该处理子模块错误', async () => {
      await global.testUtils.createTestProject(tempDir);

      // 模拟 Vite 优化器错误
      jest.spyOn(migrator.viteOptimizer, 'optimize').mockRejectedValueOnce(new Error('Vite error'));

      const report = await migrator.migrate();

      // 应该继续执行其他步骤
      expect(report.summary).toBeDefined();
    });
  });

  describe('统计信息', () => {
    test('应该正确统计处理的文件', async () => {
      await global.testUtils.createTestProject(tempDir);

      migrator.options.dryRun = false; // 启用实际修改
      const report = await migrator.migrate();

      expect(report.summary.totalFiles).toBeGreaterThan(0);
      // 允许一定的误差，因为可能有动态生成的文件
      expect(report.summary.processedFiles + report.summary.skippedFiles).toBeLessThanOrEqual(report.summary.totalFiles + 3);
    });

    test('应该记录优化项', async () => {
      await global.testUtils.createTestProject(tempDir);

      const report = await migrator.migrate();

      expect(report.optimizations.length).toBeGreaterThan(0);
    });
  });

  describe('报告生成', () => {
    test('应该生成最终报告', () => {
      migrator.stats.totalFiles = 5;
      migrator.stats.processedFiles = 3;
      migrator.stats.skippedFiles = 2;
      migrator.stats.errorFiles = 0;
      migrator.stats.optimizations = [
        { type: 'vite', description: 'Vite 配置优化' }
      ];

      const report = migrator.generateFinalReport();

      expect(report.summary.totalFiles).toBe(5);
      expect(report.summary.processedFiles).toBe(3);
      expect(report.summary.skippedFiles).toBe(2);
      expect(report.optimizations.length).toBe(1);
      expect(report.timestamp).toBeDefined();
    });

    test('应该打印最终统计信息', () => {
      const report = {
        summary: {
          totalFiles: 5,
          processedFiles: 3,
          skippedFiles: 2,
          errorFiles: 0
        },
        optimizations: [
          { description: 'Test optimization' }
        ],
        errors: []
      };

      // 这个测试主要确保打印函数不会抛出错误
      expect(() => migrator.printFinalStats(report)).not.toThrow();
    });
  });

  describe('功能开关', () => {
    test('应该支持禁用架构重构', async () => {
      migrator.options.enableArchitectureRefactor = false;
      await global.testUtils.createTestProject(tempDir);

      const report = await migrator.migrate();

      expect(report.optimizations.some(opt => opt.type === 'architecture')).toBe(false);
    });

    test('应该支持禁用 Element Plus 迁移', async () => {
      migrator.options.enableElementPlusMigration = false;
      await global.testUtils.createElementUIProject(tempDir);

      const report = await migrator.migrate();

      expect(report.optimizations.some(opt => opt.type === 'element-plus')).toBe(false);
    });

    test('应该支持禁用 Vite 优化', async () => {
      migrator.options.enableViteOptimization = false;
      await global.testUtils.createTestProject(tempDir);

      const report = await migrator.migrate();

      expect(report.optimizations.some(opt => opt.type === 'vite')).toBe(false);
    });
  });

  describe('外部工具集成', () => {
    test('应该使用外部 sass-migrator', async () => {
      // 模拟 sass-migrator 可用
      const mockExec = global.testUtils.mockExecSync('sass-migrator --version', 'sass-migrator 1.0.0');

      // 创建一个简单的测试，检查是否调用了外部迁移方法
      const testFile = path.join(tempDir, 'test.scss');
      await fs.writeFile(testFile, '@import "variables";');

      // 模拟 executeSassMigrator 方法
      const originalExecute = migrator.executeSassMigrator;
      migrator.executeSassMigrator = jest.fn().mockResolvedValue('');

      await migrator.migrateFileExternal(testFile);

      expect(migrator.executeSassMigrator).toHaveBeenCalledWith(['module', testFile, '--dry-run']);

      // 恢复原始方法
      migrator.executeSassMigrator = originalExecute;
      mockExec.mockRestore();
    });
  });
});
