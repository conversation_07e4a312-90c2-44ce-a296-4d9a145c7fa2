const path = require('path');
const fs = require('fs-extra');
const ElementPlusMigrator = require('../../src/sass/migrators/elementPlusMigrator');

describe('ElementPlusMigrator', () => {
  let tempDir;
  let migrator;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('element-plus-');
    migrator = new ElementPlusMigrator(tempDir, { verbose: false });
  });

  describe('初始化', () => {
    test('应该正确初始化 Element Plus 迁移器', () => {
      expect(migrator.projectPath).toBe(tempDir);
      expect(migrator.pathMappings).toBeDefined();
      expect(migrator.variableMappings).toBeDefined();
      expect(migrator.defaultTheme).toBeDefined();
    });

    test('应该包含正确的路径映射', () => {
      expect(migrator.pathMappings['~element-ui/packages/theme-chalk/src/index'])
        .toBe('element-plus/theme-chalk/src/index.scss');
      expect(migrator.pathMappings['~element-ui/lib/theme-chalk/index.css'])
        .toBe('element-plus/dist/index.css');
    });

    test('应该包含正确的变量映射', () => {
      expect(migrator.variableMappings['$--color-primary']).toBe('$el-color-primary');
      expect(migrator.variableMappings['$--font-size-base']).toBe('$el-font-size-base');
    });
  });

  describe('Element UI 使用情况检测', () => {
    test('应该检测 package.json 中的 Element UI 依赖', async () => {
      await global.testUtils.createElementUIProject(tempDir);

      const usage = await migrator.detectElementUIUsage();

      expect(usage.hasElementUI).toBe(true);
      expect(usage.elementUIVersion).toBeDefined();
    });

    test('应该检测文件中的 Element UI 导入', async () => {
      const sassContent = `
        @import "~element-ui/packages/theme-chalk/src/index";
        @import "~element-ui/packages/theme-chalk/src/button";
      `;

      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/main.scss'), sassContent);

      const usage = await migrator.detectElementUIUsage();

      expect(usage.importStatements.length).toBeGreaterThan(0);
      expect(usage.importStatements[0].imports).toContain('~element-ui/packages/theme-chalk/src/index');
    });

    test('应该处理没有 Element UI 的项目', async () => {
      await global.testUtils.createTestProject(tempDir, {
        'package.json': {
          name: 'test-project',
          dependencies: {
            vue: '^3.0.0'
          }
        },
        'src/styles/main.scss': `@import "./variables";
@import "./mixins";

.app {
  font-size: $font-size-base;
  
  .button {
    @include button-style;
  }
}`
      });

      const usage = await migrator.detectElementUIUsage();

      expect(usage.hasElementUI).toBe(false);
      expect(usage.importStatements).toEqual([]);
    });
  });

  describe('Element 导入提取', () => {
    test('应该提取 @import 语句', () => {
      const content = `
        @import "~element-ui/packages/theme-chalk/src/index";
        @import "./variables";
        @import "~element-ui/packages/theme-chalk/src/button";
      `;

      const imports = migrator.extractElementImports(content);

      expect(imports).toContain('~element-ui/packages/theme-chalk/src/index');
      expect(imports).toContain('~element-ui/packages/theme-chalk/src/button');
      expect(imports).not.toContain('./variables');
    });

    test('应该提取 @use 语句', () => {
      const content = `
        @use "~element-ui/packages/theme-chalk/src/index" as *;
        @use "./variables" as vars;
      `;

      const imports = migrator.extractElementImports(content);

      expect(imports).toContain('~element-ui/packages/theme-chalk/src/index');
      expect(imports).not.toContain('./variables');
    });
  });

  describe('主题配置分析', () => {
    test('应该分析现有主题变量', async () => {
      const themeContent = `
        $--color-primary: #1890ff;
        $--color-success: #52c41a;
        $--font-size-base: 16px;
        $--border-radius-base: 6px;
      `;

      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/element-variables.scss'), themeContent);

      const theme = await migrator.analyzeCurrentTheme();

      expect(theme.colors.primary.base).toBe('#1890ff');
      expect(theme.colors.success.base).toBe('#52c41a');
      expect(theme.fontSize.base).toBe('16px');
      expect(theme.borderRadius.base).toBe('6px');
    });

    test('应该使用默认主题配置', async () => {
      const theme = await migrator.analyzeCurrentTheme();

      expect(theme.colors.primary.base).toBe('#409eff');
      expect(theme.fontSize.base).toBe('14px');
      expect(theme.borderRadius.base).toBe('4px');
    });
  });

  describe('主题变量提取', () => {
    test('应该提取颜色变量', () => {
      const content = `
        $--color-primary: #1890ff;
        $--color-success: #52c41a;
        $--color-warning: #faad14;
      `;

      const theme = { colors: { primary: {}, success: {}, warning: {} } };
      migrator.extractThemeVariables(content, theme);

      expect(theme.colors.primary.base).toBe('#1890ff');
      expect(theme.colors.success.base).toBe('#52c41a');
      expect(theme.colors.warning.base).toBe('#faad14');
    });

    test('应该提取字体大小变量', () => {
      const content = `
        $--font-size-base: 16px;
        $--font-size-small: 12px;
        $--font-size-large: 18px;
      `;

      const theme = { fontSize: { base: '', small: '', large: '' } };
      migrator.extractThemeVariables(content, theme);

      expect(theme.fontSize.base).toBe('16px');
      expect(theme.fontSize.small).toBe('12px');
      expect(theme.fontSize.large).toBe('18px');
    });
  });

  describe('Element Plus 配置生成', () => {
    test('应该生成 Element Plus 配置文件', async () => {
      const theme = {
        colors: {
          primary: { base: '#1890ff' },
          success: { base: '#52c41a' }
        },
        fontSize: {
          base: '16px',
          small: '12px',
          large: '18px'
        },
        borderRadius: {
          base: '6px'
        }
      };

      await migrator.generateElementPlusConfig(theme);

      const configPath = path.join(tempDir, migrator.options.elementDir, 'index.scss');
      expect(await fs.pathExists(configPath)).toBe(true);

      const content = await fs.readFile(configPath, 'utf8');
      expect(content).toContain('@forward \'element-plus/theme-chalk/src/common/var.scss\' with (');
      expect(content).toContain('\'primary\': (');
      expect(content).toContain('\'base\': #1890ff,');
      expect(content).toContain('$font-size-base: 16px,');
      expect(content).toContain('$border-radius-base: 6px,');
    });

    test('应该包含字体路径修复', async () => {
      await migrator.generateElementPlusConfig(migrator.defaultTheme);

      const configPath = path.join(tempDir, migrator.options.elementDir, 'index.scss');
      const content = await fs.readFile(configPath, 'utf8');

      expect(content).toContain('$font-path: \'element-plus/dist/fonts\'');
    });
  });

  describe('导入语句更新', () => {
    test('应该更新 Element UI 导入语句', async () => {
      const sassContent = `
        @import "~element-ui/packages/theme-chalk/src/index";
        @import "./variables";
        .button { color: red; }
      `;

      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/main.scss'), sassContent);

      await migrator.updateImportStatements();

      const updatedContent = await fs.readFile(path.join(tempDir, 'src/styles/main.scss'), 'utf8');
      expect(updatedContent).toContain('@use "src/styles/element" as *;');
      expect(updatedContent).not.toContain('~element-ui');
      expect(updatedContent).toContain('./variables'); // 应该保留非 Element 导入
    });

    test('应该更新变量名', async () => {
      const sassContent = `
        .button {
          color: $--color-primary;
          font-size: $--font-size-base;
          border-radius: $--border-radius-base;
        }
      `;

      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/button.scss'), sassContent);

      await migrator.updateImportStatements();

      const updatedContent = await fs.readFile(path.join(tempDir, 'src/styles/button.scss'), 'utf8');
      expect(updatedContent).toContain('$el-color-primary');
      expect(updatedContent).toContain('$el-font-size-base');
      expect(updatedContent).toContain('$el-border-radius-base');
    });
  });

  describe('迁移报告生成', () => {
    test('应该生成详细的迁移报告', async () => {
      const usage = {
        hasElementUI: true,
        hasElementPlus: false,
        elementUIVersion: '^2.15.0',
        importStatements: [
          { file: 'main.scss', imports: ['~element-ui/packages/theme-chalk/src/index'] }
        ]
      };

      const theme = migrator.defaultTheme;

      const report = await migrator.generateMigrationReport(usage, theme);

      expect(report.reportPath).toBeDefined();
      expect(report.updatedFiles).toBe(1);
      expect(report.themeVariables).toBeGreaterThan(0);

      const reportPath = path.join(tempDir, 'element-plus-migration-report.md');
      expect(await fs.pathExists(reportPath)).toBe(true);

      const content = await fs.readFile(reportPath, 'utf8');
      expect(content).toContain('# Element Plus 迁移报告');
      expect(content).toContain('Element UI 版本: ^2.15.0');
      expect(content).toContain('需要安装 Element Plus');
    });

    test('应该包含迁移建议', async () => {
      const usage = { hasElementUI: true, hasElementPlus: true, importStatements: [] };
      const theme = migrator.defaultTheme;

      await migrator.generateMigrationReport(usage, theme);

      const reportPath = path.join(tempDir, 'element-plus-migration-report.md');
      const content = await fs.readFile(reportPath, 'utf8');

      expect(content).toContain('## 迁移建议');
      expect(content).toContain('import "src/styles/element/index.scss"');
      expect(content).toContain('## 注意事项');
    });
  });

  describe('完整迁移流程', () => {
    test('应该执行完整的 Element Plus 迁移', async () => {
      await global.testUtils.createElementUIProject(tempDir);

      const result = await migrator.migrate();

      expect(result.skipped).toBeUndefined();
      expect(result.reportPath).toBeDefined();
      expect(result.updatedFiles).toBeGreaterThan(0);

      // 检查生成的配置文件
      const configPath = path.join(tempDir, migrator.options.elementDir, 'index.scss');
      expect(await fs.pathExists(configPath)).toBe(true);

      // 检查迁移报告
      const reportPath = path.join(tempDir, 'element-plus-migration-report.md');
      expect(await fs.pathExists(reportPath)).toBe(true);
    });

    test('应该跳过没有 Element UI 的项目', async () => {
      await global.testUtils.createTestProject(tempDir, {
        'package.json': {
          name: 'test-project',
          dependencies: {
            vue: '^3.0.0'
          }
        },
        'src/styles/main.scss': `@import "./variables";
@import "./mixins";

.app {
  font-size: $font-size-base;
}`
      });

      const result = await migrator.migrate();

      expect(result.skipped).toBe(true);
    });
  });

  describe('错误处理', () => {
    test('应该处理文件读取错误', async () => {
      // 创建一个包含Element UI导入的文件
      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/test.scss'), '@import "~element-ui/packages/theme-chalk/src/index";');

      // 模拟文件读取错误 - 直接针对特定文件
      const originalReadFile = fs.readFile;
      jest.spyOn(fs, 'readFile').mockImplementation((filePath, options) => {
        if (filePath.includes('test.scss')) {
          return Promise.reject(new Error('Permission denied'));
        }
        return originalReadFile(filePath, options);
      });

      // 应该抛出错误而不是静默处理
      await expect(migrator.updateImportStatements()).rejects.toThrow('Permission denied');

      fs.readFile.mockRestore();
    });

    test('应该处理无效的主题文件', async () => {
      const invalidContent = 'invalid scss content {{{';

      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/variables.scss'), invalidContent);

      await expect(migrator.analyzeCurrentTheme()).resolves.not.toThrow();
    });
  });

  describe('Sass 文件查找', () => {
    test('应该找到所有 Sass 文件', async () => {
      await global.testUtils.createTestProject(tempDir);

      const files = await migrator.findSassFiles();

      expect(files.length).toBeGreaterThan(0);
      expect(files.some(file => file.endsWith('.scss'))).toBe(true);
    });

    test('应该排除 node_modules 目录', async () => {
      await fs.ensureDir(path.join(tempDir, 'node_modules/some-package'));
      await fs.writeFile(path.join(tempDir, 'node_modules/some-package/style.scss'), 'content');

      const files = await migrator.findSassFiles();

      expect(files.some(file => file.includes('node_modules'))).toBe(false);
    });
  });
});
