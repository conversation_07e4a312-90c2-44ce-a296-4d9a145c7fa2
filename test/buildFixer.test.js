const BuildFixer = require('../src/build/buildFixer');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

describe('BuildFixer', () => {
  let buildFixer;

  beforeEach(() => {
    buildFixer = new BuildFixer('/test/path', {
      buildCommand: 'npm run build',
      maxRetries: 2
    });
  });

  it('should construct with correct options', () => {
    expect(buildFixer.projectPath).toBe('/test/path');
    expect(buildFixer.options.buildCommand).toBe('npm run build');
    expect(buildFixer.options.maxRetries).toBe(2);
  });

  it('should detect error start correctly', () => {
    const tsError = buildFixer.detectErrorStart('src/test.ts(10,5): error TS2304: Cannot find name "Vue".');
    expect(tsError.type).toBe('typescript');
    expect(tsError.file).toBe('src/test.ts');
  });

  it('should categorize errors correctly', () => {
    const errors = [{ message: 'Cannot find module "element-ui"', type: 'webpack' }];
    const categorized = buildFixer.categorizeErrors(errors);
    expect(categorized[0].category).toBe('missing-module');
  });

  it('should generate build error prompt correctly', () => {
    const error = {
      type: 'vue',
      category: 'ui-library',
      message: 'el-button not found',
      file: 'src/Test.vue'
    };
    const prompt = buildFixer.generateBuildErrorPrompt('test content', error);
    expect(prompt).toContain('构建错误修复');
  });

  it('should validate repaired content correctly', () => {
    const valid = buildFixer.validateRepairedContent(
      'export default { name: "TestFixed" }',
      'export default { name: "Test" }'
    );
    expect(valid).toBe(true);
  });

  it('should get initial build stats correctly', () => {
    const stats = buildFixer.getBuildStats();
    expect(typeof stats).toBe('object');
    expect(stats.buildAttempts).toBe(0);
  });
});
